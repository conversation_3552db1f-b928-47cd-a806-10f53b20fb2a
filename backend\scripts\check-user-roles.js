/**
 * Check User Roles Script
 * 
 * This script verifies the current roles of both test users
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

async function checkUserRoles() {
    console.log('🔍 Checking User Roles for RBAC Testing\n');

    try {
        // Get all auth users
        const { data: authUsers, error: authError } = await supabaseAdmin.auth.admin.listUsers();
        
        if (authError) {
            console.error('❌ Error fetching auth users:', authError.message);
            return;
        }

        // Get all user profiles
        const { data: profiles, error: profileError } = await supabaseAdmin
            .from('user_profiles')
            .select('*');

        if (profileError) {
            console.error('❌ Error fetching user profiles:', profileError.message);
            return;
        }

        console.log('👥 User Role Summary:');
        console.log('====================');

        for (const user of authUsers.users) {
            const profile = profiles.find(p => p.id === user.id);
            
            console.log(`\n📧 ${user.email}`);
            console.log(`   ID: ${user.id}`);
            console.log(`   Created: ${user.created_at}`);
            console.log(`   Confirmed: ${user.email_confirmed_at ? 'Yes' : 'No'}`);
            
            if (profile) {
                console.log(`   ✅ Profile exists:`);
                console.log(`      Role: ${profile.role}`);
                console.log(`      Active: ${profile.is_active}`);
                console.log(`      Employee ID: ${profile.employee_id || 'none'}`);
                console.log(`      Updated: ${profile.updated_at}`);
            } else {
                console.log(`   ❌ No profile found`);
            }

            // Check metadata for comparison
            const metadataRole = user.raw_user_meta_data?.role;
            if (metadataRole) {
                console.log(`   📋 Metadata role: ${metadataRole}`);
            } else {
                console.log(`   📋 No metadata role`);
            }
        }

        console.log('\n🎯 RBAC Testing Setup:');
        console.log('======================');
        
        const adminUser = authUsers.users.find(u => u.email === '<EMAIL>');
        const regularUser = authUsers.users.find(u => u.email === '<EMAIL>');
        
        if (adminUser) {
            const adminProfile = profiles.find(p => p.id === adminUser.id);
            console.log(`✅ ADMIN user ready: <EMAIL> (Role: ${adminProfile?.role || 'unknown'})`);
        } else {
            console.log(`❌ ADMIN user not found: <EMAIL>`);
        }

        if (regularUser) {
            const userProfile = profiles.find(p => p.id === regularUser.id);
            console.log(`✅ USER ready: <EMAIL> (Role: ${userProfile?.role || 'unknown'})`);
        } else {
            console.log(`❌ USER not found: <EMAIL>`);
        }

        console.log('\n📋 Next Steps for NON-ADMIN Testing:');
        console.log('1. Log out of any current session');
        console.log('2. Go to: http://localhost:9002/auth-test');
        console.log('3. Log in as: <EMAIL> / UserPassword123!');
        console.log('4. Run the NON-ADMIN test script in browser console');

    } catch (error) {
        console.error('❌ Script failed:', error.message);
    }
}

checkUserRoles().catch(console.error);
