/*
  Warnings:

  - A unique constraint covering the columns `[assignedVehicleId]` on the table `Employee` will be added. If there are existing duplicate values, this will fail.
  - Changed the type of `role` on the `Employee` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "EmployeeRole" AS ENUM ('driver', 'mechanic', 'administrator', 'office_staff', 'manager', 'service_advisor', 'technician', 'other');

-- CreateEnum
CREATE TYPE "EmployeeStatus" AS ENUM ('Active', 'On_Leave', 'Terminated', 'Inactive');

-- CreateEnum
CREATE TYPE "DriverAvailability" AS ENUM ('On_Shift', 'Off_Shift', 'On_Break', 'Busy');

-- CreateEnum
CREATE TYPE "DelegationStatus" AS ENUM ('Planned', 'Confirmed', 'In_Progress', 'Completed', 'Cancelled', 'No_details');

-- CreateEnum
CREATE TYPE "TaskStatus" AS ENUM ('Pending', 'Assigned', 'In_Progress', 'Completed', 'Cancelled');

-- CreateEnum
CREATE TYPE "TaskPriority" AS ENUM ('Low', 'Medium', 'High');

-- AlterTable
ALTER TABLE "Employee" ADD COLUMN     "assignedVehicleId" INTEGER,
ADD COLUMN     "availability" "DriverAvailability",
ADD COLUMN     "contactEmail" TEXT,
ADD COLUMN     "contactMobile" TEXT,
ADD COLUMN     "contactPhone" TEXT,
ADD COLUMN     "currentLocation" TEXT,
ADD COLUMN     "department" TEXT,
ADD COLUMN     "fullName" TEXT,
ADD COLUMN     "generalAssignments" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "hireDate" TIMESTAMP(3),
ADD COLUMN     "notes" TEXT,
ADD COLUMN     "position" TEXT,
ADD COLUMN     "profileImageUrl" TEXT,
ADD COLUMN     "shiftSchedule" TEXT,
ADD COLUMN     "skills" TEXT[] DEFAULT ARRAY[]::TEXT[],
ADD COLUMN     "status" "EmployeeStatus",
ADD COLUMN     "workingHours" TEXT,
DROP COLUMN "role",
ADD COLUMN     "role" "EmployeeRole" NOT NULL;

-- AlterTable
ALTER TABLE "Vehicle" ADD COLUMN     "color" TEXT,
ADD COLUMN     "imageUrl" TEXT,
ADD COLUMN     "initialOdometer" INTEGER;

-- CreateTable
CREATE TABLE "EmployeeStatusEntry" (
    "id" TEXT NOT NULL,
    "employeeId" INTEGER NOT NULL,
    "status" "EmployeeStatus" NOT NULL,
    "changedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "reason" TEXT,

    CONSTRAINT "EmployeeStatusEntry_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ServiceRecord" (
    "id" TEXT NOT NULL,
    "vehicleId" INTEGER NOT NULL,
    "employeeId" INTEGER,
    "date" TIMESTAMP(3) NOT NULL,
    "odometer" INTEGER NOT NULL,
    "servicePerformed" TEXT[],
    "notes" TEXT,
    "cost" DECIMAL(65,30),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ServiceRecord_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Delegation" (
    "id" TEXT NOT NULL,
    "eventName" TEXT NOT NULL,
    "location" TEXT NOT NULL,
    "durationFrom" TIMESTAMP(3) NOT NULL,
    "durationTo" TIMESTAMP(3) NOT NULL,
    "invitationFrom" TEXT,
    "invitationTo" TEXT,
    "flightArrivalId" TEXT,
    "flightDepartureId" TEXT,
    "status" "DelegationStatus" NOT NULL,
    "notes" TEXT,
    "imageUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Delegation_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Delegate" (
    "id" TEXT NOT NULL,
    "delegationId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "notes" TEXT,

    CONSTRAINT "Delegate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DelegationStatusEntry" (
    "id" TEXT NOT NULL,
    "delegationId" TEXT NOT NULL,
    "status" "DelegationStatus" NOT NULL,
    "changedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "reason" TEXT,

    CONSTRAINT "DelegationStatusEntry_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FlightDetails" (
    "id" TEXT NOT NULL,
    "flightNumber" TEXT NOT NULL,
    "dateTime" TIMESTAMP(3) NOT NULL,
    "airport" TEXT NOT NULL,
    "terminal" TEXT,
    "notes" TEXT,
    "arrivalDelegationId" TEXT,
    "departureDelegationId" TEXT,

    CONSTRAINT "FlightDetails_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Task" (
    "id" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "location" TEXT NOT NULL,
    "dateTime" TIMESTAMP(3) NOT NULL,
    "estimatedDuration" INTEGER NOT NULL,
    "requiredSkills" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "priority" "TaskPriority" NOT NULL,
    "deadline" TIMESTAMP(3),
    "status" "TaskStatus" NOT NULL,
    "notes" TEXT,
    "vehicleId" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "assignedEmployeeIds" TEXT[] DEFAULT ARRAY[]::TEXT[],

    CONSTRAINT "Task_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SubTask" (
    "id" TEXT NOT NULL,
    "taskId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "completed" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "SubTask_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TaskStatusEntry" (
    "id" TEXT NOT NULL,
    "taskId" TEXT NOT NULL,
    "status" "TaskStatus" NOT NULL,
    "changedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "reason" TEXT,

    CONSTRAINT "TaskStatusEntry_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_EmployeeTasks" (
    "A" INTEGER NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_EmployeeTasks_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE UNIQUE INDEX "Delegation_flightArrivalId_key" ON "Delegation"("flightArrivalId");

-- CreateIndex
CREATE UNIQUE INDEX "Delegation_flightDepartureId_key" ON "Delegation"("flightDepartureId");

-- CreateIndex
CREATE UNIQUE INDEX "FlightDetails_arrivalDelegationId_key" ON "FlightDetails"("arrivalDelegationId");

-- CreateIndex
CREATE UNIQUE INDEX "FlightDetails_departureDelegationId_key" ON "FlightDetails"("departureDelegationId");

-- CreateIndex
CREATE INDEX "_EmployeeTasks_B_index" ON "_EmployeeTasks"("B");

-- CreateIndex
CREATE UNIQUE INDEX "Employee_assignedVehicleId_key" ON "Employee"("assignedVehicleId");

-- AddForeignKey
ALTER TABLE "Employee" ADD CONSTRAINT "Employee_assignedVehicleId_fkey" FOREIGN KEY ("assignedVehicleId") REFERENCES "Vehicle"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmployeeStatusEntry" ADD CONSTRAINT "EmployeeStatusEntry_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ServiceRecord" ADD CONSTRAINT "ServiceRecord_vehicleId_fkey" FOREIGN KEY ("vehicleId") REFERENCES "Vehicle"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ServiceRecord" ADD CONSTRAINT "ServiceRecord_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Delegation" ADD CONSTRAINT "Delegation_flightArrivalId_fkey" FOREIGN KEY ("flightArrivalId") REFERENCES "FlightDetails"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Delegation" ADD CONSTRAINT "Delegation_flightDepartureId_fkey" FOREIGN KEY ("flightDepartureId") REFERENCES "FlightDetails"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Delegate" ADD CONSTRAINT "Delegate_delegationId_fkey" FOREIGN KEY ("delegationId") REFERENCES "Delegation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DelegationStatusEntry" ADD CONSTRAINT "DelegationStatusEntry_delegationId_fkey" FOREIGN KEY ("delegationId") REFERENCES "Delegation"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Task" ADD CONSTRAINT "Task_vehicleId_fkey" FOREIGN KEY ("vehicleId") REFERENCES "Vehicle"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SubTask" ADD CONSTRAINT "SubTask_taskId_fkey" FOREIGN KEY ("taskId") REFERENCES "Task"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TaskStatusEntry" ADD CONSTRAINT "TaskStatusEntry_taskId_fkey" FOREIGN KEY ("taskId") REFERENCES "Task"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_EmployeeTasks" ADD CONSTRAINT "_EmployeeTasks_A_fkey" FOREIGN KEY ("A") REFERENCES "Employee"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_EmployeeTasks" ADD CONSTRAINT "_EmployeeTasks_B_fkey" FOREIGN KEY ("B") REFERENCES "Task"("id") ON DELETE CASCADE ON UPDATE CASCADE;
