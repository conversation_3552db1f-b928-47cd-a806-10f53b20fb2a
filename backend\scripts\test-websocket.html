<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WebSocket Test for Supabase Real-time</title>
  <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #333;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 15px;
      background-color: #f9f9f9;
    }
    .status {
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 15px;
    }
    .connected {
      background-color: #d4edda;
      color: #155724;
    }
    .disconnected {
      background-color: #f8d7da;
      color: #721c24;
    }
    .event-log {
      height: 300px;
      overflow-y: auto;
      border: 1px solid #ddd;
      padding: 10px;
      background-color: #f5f5f5;
      font-family: monospace;
    }
    .event {
      margin-bottom: 5px;
      padding: 5px;
      border-bottom: 1px solid #eee;
    }
    .event-time {
      color: #666;
      font-size: 0.8em;
    }
    .event-name {
      font-weight: bold;
      color: #007bff;
    }
    .event-data {
      margin-top: 5px;
      white-space: pre-wrap;
    }
    button {
      background-color: #007bff;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #0069d9;
    }
    input {
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      width: 100%;
      box-sizing: border-box;
      margin-bottom: 10px;
    }
  </style>
</head>
<body>
  <h1>WebSocket Test for Supabase Real-time</h1>
  
  <div class="card">
    <h2>Connection</h2>
    <div id="status" class="status disconnected">Disconnected</div>
    
    <div>
      <label for="serverUrl">Server URL:</label>
      <input type="text" id="serverUrl" value="http://localhost:3001" />
    </div>
    
    <button id="connectBtn">Connect</button>
    <button id="disconnectBtn" disabled>Disconnect</button>
  </div>
  
  <div class="card">
    <h2>Event Log</h2>
    <div class="event-log" id="eventLog"></div>
    <button id="clearBtn">Clear Log</button>
  </div>
  
  <script>
    let socket;
    const connectBtn = document.getElementById('connectBtn');
    const disconnectBtn = document.getElementById('disconnectBtn');
    const clearBtn = document.getElementById('clearBtn');
    const serverUrlInput = document.getElementById('serverUrl');
    const statusDiv = document.getElementById('status');
    const eventLogDiv = document.getElementById('eventLog');
    
    // Socket.io event names
    const SOCKET_EVENTS = {
      VEHICLE_UPDATED: 'vehicle:updated',
      VEHICLE_CREATED: 'vehicle:created',
      VEHICLE_DELETED: 'vehicle:deleted',
      EMPLOYEE_UPDATED: 'employee:updated',
      EMPLOYEE_CREATED: 'employee:created',
      EMPLOYEE_DELETED: 'employee:deleted',
      REFRESH_VEHICLES: 'refresh:vehicles',
      REFRESH_EMPLOYEES: 'refresh:employees',
    };
    
    // Connect to WebSocket server
    connectBtn.addEventListener('click', () => {
      const serverUrl = serverUrlInput.value.trim();
      
      if (!serverUrl) {
        alert('Please enter a server URL');
        return;
      }
      
      try {
        socket = io(serverUrl);
        
        // Connection events
        socket.on('connect', () => {
          statusDiv.textContent = `Connected (ID: ${socket.id})`;
          statusDiv.className = 'status connected';
          connectBtn.disabled = true;
          disconnectBtn.disabled = false;
          logEvent('connect', 'Connected to server');
        });
        
        socket.on('disconnect', (reason) => {
          statusDiv.textContent = `Disconnected: ${reason}`;
          statusDiv.className = 'status disconnected';
          connectBtn.disabled = false;
          disconnectBtn.disabled = true;
          logEvent('disconnect', reason);
        });
        
        socket.on('connect_error', (error) => {
          statusDiv.textContent = `Connection error: ${error.message}`;
          statusDiv.className = 'status disconnected';
          logEvent('connect_error', error.message);
        });
        
        // Subscribe to all events
        Object.values(SOCKET_EVENTS).forEach(eventName => {
          socket.on(eventName, (data) => {
            logEvent(eventName, data);
          });
        });
        
      } catch (error) {
        alert(`Error connecting to server: ${error.message}`);
        console.error('Connection error:', error);
      }
    });
    
    // Disconnect from WebSocket server
    disconnectBtn.addEventListener('click', () => {
      if (socket) {
        socket.disconnect();
      }
    });
    
    // Clear event log
    clearBtn.addEventListener('click', () => {
      eventLogDiv.innerHTML = '';
    });
    
    // Log event to UI
    function logEvent(eventName, data) {
      const eventDiv = document.createElement('div');
      eventDiv.className = 'event';
      
      const now = new Date();
      const timeStr = now.toLocaleTimeString();
      
      const timeSpan = document.createElement('span');
      timeSpan.className = 'event-time';
      timeSpan.textContent = `[${timeStr}] `;
      
      const nameSpan = document.createElement('span');
      nameSpan.className = 'event-name';
      nameSpan.textContent = eventName;
      
      eventDiv.appendChild(timeSpan);
      eventDiv.appendChild(nameSpan);
      
      if (data) {
        const dataDiv = document.createElement('div');
        dataDiv.className = 'event-data';
        
        if (typeof data === 'object') {
          dataDiv.textContent = JSON.stringify(data, null, 2);
        } else {
          dataDiv.textContent = data;
        }
        
        eventDiv.appendChild(dataDiv);
      }
      
      eventLogDiv.appendChild(eventDiv);
      eventLogDiv.scrollTop = eventLogDiv.scrollHeight;
    }
  </script>
</body>
</html>
