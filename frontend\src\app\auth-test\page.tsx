'use client';

import React from 'react';
import { AuthProvider, ProtectedRoute, UserProfile } from '../../components/auth';

/**
 * EMERGENCY SECURITY TEST PAGE
 * 
 * This page demonstrates the authentication components working together.
 * It shows the login form when not authenticated and user profile when authenticated.
 */
export default function AuthTestPage() {
	return (
		<AuthProvider>
			<div className="min-h-screen bg-gray-50 py-8">
				<div className="container mx-auto px-4">
					<div className="text-center mb-8">
						<h1 className="text-3xl font-bold text-gray-900 mb-2">
							🚨 Emergency Security Test
						</h1>
						<p className="text-gray-600">
							Testing Supabase Authentication Implementation
						</p>
					</div>

					<ProtectedRoute>
						<div className="max-w-2xl mx-auto space-y-6">
							<div className="bg-white rounded-lg shadow-md p-6">
								<h2 className="text-xl font-semibold mb-4">
									✅ Authentication Successful
								</h2>
								<p className="text-gray-600 mb-4">
									You are now authenticated and can access protected content.
								</p>
								
								{/* User Profile Component */}
								<UserProfile variant="card" />
							</div>

							<div className="bg-green-50 border border-green-200 rounded-lg p-4">
								<h3 className="text-green-800 font-medium mb-2">
									🎉 Emergency Security Implementation Status
								</h3>
								<ul className="text-green-700 text-sm space-y-1">
									<li>✅ Supabase client configured</li>
									<li>✅ Authentication hook implemented</li>
									<li>✅ Login form functional</li>
									<li>✅ Protected routes working</li>
									<li>✅ User profile display active</li>
									<li>✅ Session management operational</li>
								</ul>
							</div>
						</div>
					</ProtectedRoute>
				</div>
			</div>
		</AuthProvider>
	);
}
