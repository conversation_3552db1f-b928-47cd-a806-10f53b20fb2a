/**
 * ========================================================================
 * TEMPORARY HOTFIX: Patches Array.prototype.findIndex
 * ========================================================================
 *
 * PURPOSE: To prevent 'findIndex is not a function' errors in production
 * due to unexpected non-array data.
 *
 * RISK: Modifying native prototypes can have unintended side effects.
 * Test thoroughly.
 *
 * REMOVAL PLAN: This script MUST be removed once backend and frontend store
 * fixes (Phases 2 & 3) are verified and stable.
 * Target removal: Sprint 24 (June 2024)
 *
 * This script patches Array.prototype.findIndex to make it more resilient
 * and adds defensive wrappers around common array methods to prevent errors
 * when they're called on non-array values.
 * ========================================================================
 */

(function () {
	// Store the original findIndex method
	const originalFindIndex = Array.prototype.findIndex;

	// Create a safe version of findIndex that checks if 'this' is an array
	Array.prototype.findIndex = function () {
		// If 'this' is not an array-like object, return -1 instead of throwing an error
		if (!this || typeof this.length !== 'number') {
			console.warn('findIndex called on non-array', this);
			return -1;
		}
		// Otherwise use the original method
		return originalFindIndex.apply(this, arguments);
	};

	// Create a global helper function to safely use array methods
	window.safeArrayOp = function (possibleArray, defaultValue, operation) {
		if (Array.isArray(possibleArray)) {
			return operation(possibleArray);
		}
		console.warn('Array operation attempted on non-array:', possibleArray);
		return defaultValue;
	};

	// Patch any global objects that might be using findIndex
	// This is a defensive measure for third-party libraries
	if (window.$ && typeof window.$ === 'function') {
		const original$ = window.$;
		window.$ = function () {
			const result = original$.apply(this, arguments);
			// Add safety to jQuery-like objects if they have findIndex
			if (result && typeof result.findIndex === 'function') {
				const originalFindIndex = result.findIndex;
				result.findIndex = function () {
					if (!this || typeof this.length !== 'number') {
						console.warn('jQuery findIndex called on invalid object', this);
						return -1;
					}
					return originalFindIndex.apply(this, arguments);
				};
			}
			return result;
		};
	}

	console.log('Array.findIndex protection installed');
})();
