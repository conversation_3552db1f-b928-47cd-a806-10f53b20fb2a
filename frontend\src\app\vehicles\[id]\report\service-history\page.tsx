'use client';

import {useEffect, useState, useCallback} from 'react';
import {useParams} from 'next/navigation';
import type {Vehicle, EnrichedServiceRecord} from '@/lib/types';
import {getVehicleById} from '@/lib/store';
import {getVehicleServiceRecords} from '@/lib/services/serviceRecordService';
import Image from 'next/image';
import {ReportActions} from '@/components/reports/ReportActions';
import {PageHeader} from '@/components/ui/PageHeader';
import {Card, CardContent} from '@/components/ui/card';
import {SkeletonLoader, DataLoader} from '@/components/ui/loading';
import {EnhancedServiceHistoryContainer} from '@/components/service-history/EnhancedServiceHistoryContainer';
import {History, Car} from 'lucide-react';
import Link from 'next/link';
import {Button} from '@/components/ui/button';
import {ActionButton} from '@/components/ui/action-button';
import ErrorBoundary from '@/components/ErrorBoundary';

export default function VehicleServiceHistoryPage() {
	const params = useParams();
	const [vehicle, setVehicle] = useState<Vehicle | null>(null);
	const [serviceRecords, setServiceRecords] = useState<EnrichedServiceRecord[]>(
		[]
	);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [retryCount, setRetryCount] = useState(0);
	const vehicleId = params.id as string;

	// Fetch vehicle data
	const fetchVehicleData = useCallback(async () => {
		setIsLoading(true);
		setError(null);

		if (!vehicleId) {
			setError('No Vehicle ID provided.');
			setIsLoading(false);
			return;
		}

		try {
			// Fetch vehicle details
			const foundVehicle = await getVehicleById(Number(vehicleId));
			if (!foundVehicle) {
				setError('Vehicle not found.');
				setIsLoading(false);
				return;
			}

			setVehicle(foundVehicle);
			document.title = `${foundVehicle.make} ${foundVehicle.model} - Service History Report`;

			// Fetch enriched service records for this vehicle
			const records = await getVehicleServiceRecords(Number(vehicleId));
			setServiceRecords(records);
		} catch (err) {
			console.error('Error fetching vehicle service history:', err);
			setError(
				err instanceof Error ? err.message : 'Failed to load vehicle data.'
			);
		} finally {
			setIsLoading(false);
		}
	}, [vehicleId]);

	// Initial data fetch
	useEffect(() => {
		fetchVehicleData();
	}, [fetchVehicleData, retryCount]);

	// Handle retry
	const handleRetry = useCallback(() => {
		setRetryCount((prev) => prev + 1);
	}, []);

	// Loading state
	if (isLoading) {
		return (
			<ErrorBoundary>
				<div className='max-w-5xl mx-auto p-4'>
					<div className='space-y-6'>
						<SkeletonLoader variant='card' count={1} />
						<SkeletonLoader variant='table' count={5} />
					</div>
				</div>
			</ErrorBoundary>
		);
	}

	// Error state
	if (error || !vehicle) {
		return (
			<ErrorBoundary>
				<div className='max-w-5xl mx-auto p-4'>
					<Card className='shadow-md'>
						<CardContent className='p-6'>
							<h2 className='text-xl font-semibold text-red-600 mb-2'>Error</h2>
							<p className='text-gray-700 mb-4'>
								{error || 'Vehicle not found'}
							</p>
							<div className='flex gap-2'>
								<Button onClick={handleRetry}>Try Again</Button>
								<Button variant='outline' asChild>
									<Link href='/vehicles'>Back to Vehicles</Link>
								</Button>
							</div>
						</CardContent>
					</Card>
				</div>
			</ErrorBoundary>
		);
	}

	return (
		<ErrorBoundary>
			<div className='max-w-5xl mx-auto p-4 space-y-6 print-container'>
				<PageHeader
					title='Vehicle Service History'
					description={`${vehicle.make} ${vehicle.model} (${vehicle.year})`}
					icon={History}>
					<div className='flex gap-2 no-print'>
						<ActionButton
							actionType='tertiary'
							asChild
							icon={<Car className='h-4 w-4' />}>
							<Link href={`/vehicles/${vehicleId}`}>View Vehicle</Link>
						</ActionButton>
						<ReportActions
							reportContentId='#vehicle-service-history-content'
							reportType='vehicle-service-history'
							entityId={vehicleId}
							tableId='#service-history-table'
							fileName={`vehicle-service-history-${vehicle.make}-${vehicle.model}`}
							enableCsv={serviceRecords.length > 0}
						/>
					</div>
				</PageHeader>

				<div id='vehicle-service-history-content' className='report-content'>
					{/* Vehicle Summary Card */}
					<Card className='shadow-md mb-6 card-print'>
						<CardContent className='p-4'>
							<div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
								<div className='col-span-1 md:col-span-2'>
									<h2 className='text-xl font-semibold text-gray-800 mb-4'>
										Vehicle Details
									</h2>
									<div className='grid grid-cols-2 gap-x-4 gap-y-2 text-sm'>
										<div>
											<span className='font-medium'>Make:</span> {vehicle.make}
										</div>
										<div>
											<span className='font-medium'>Model:</span>{' '}
											{vehicle.model}
										</div>
										<div>
											<span className='font-medium'>Year:</span> {vehicle.year}
										</div>
										{vehicle.licensePlate && (
											<div>
												<span className='font-medium'>Plate Number:</span>{' '}
												{vehicle.licensePlate}
											</div>
										)}
										{vehicle.color && (
											<div>
												<span className='font-medium'>Color:</span>{' '}
												{vehicle.color}
											</div>
										)}
										<div>
											<strong>Initial Odometer:</strong>{' '}
											{vehicle.initialOdometer !== null
												? `${vehicle.initialOdometer.toLocaleString()} miles`
												: 'Not recorded'}
										</div>
										<div>
											<span className='font-medium'>Current Odometer:</span>{' '}
											{serviceRecords.length > 0
												? `${Math.max(
														...serviceRecords.map((r) => r.odometer)
												  ).toLocaleString()} miles`
												: vehicle.initialOdometer !== null
												? `${vehicle.initialOdometer.toLocaleString()} miles`
												: 'No odometer data available'}
										</div>
										<div className='col-span-2'>
											<span className='font-medium'>Last Updated:</span>{' '}
											{serviceRecords.length > 0
												? new Date(
														Math.max(
															...serviceRecords.map((r) =>
																new Date(r.date).getTime()
															)
														)
												  ).toLocaleDateString()
												: 'No service records'}
										</div>
									</div>
								</div>

								{vehicle.imageUrl && (
									<div className='col-span-1 no-print'>
										<div className='relative aspect-[4/3] w-full overflow-hidden rounded'>
											<Image
												src={vehicle.imageUrl}
												alt={`${vehicle.make} ${vehicle.model}`}
												fill
												sizes='(max-width: 768px) 100vw, 300px'
												style={{objectFit: 'cover'}}
											/>
										</div>
									</div>
								)}
							</div>
						</CardContent>
					</Card>

					{/* Service History */}
					<header className='text-center mb-8 pb-4 border-b-2 border-gray-300 print-only'>
						<h1 className='text-3xl font-bold text-gray-800'>
							Vehicle Service History Report
						</h1>
						<p className='text-md text-gray-600'>
							{vehicle.make} {vehicle.model} ({vehicle.year})
							{vehicle.licensePlate && ` - ${vehicle.licensePlate}`}
						</p>
					</header>

					<EnhancedServiceHistoryContainer
						records={serviceRecords}
						isLoading={false}
						error={null}
						onRetry={handleRetry}
						showVehicleInfo={false}
						vehicleSpecific={true}
					/>

					<footer className='mt-10 pt-4 border-t-2 border-gray-300 text-center text-xs text-gray-500'>
						<p>Report generated on: {new Date().toLocaleDateString()}</p>
						<p>WorkHub - Vehicle Service Management</p>
					</footer>
				</div>

				{/* Print Styles */}
				<style jsx global>{`
					.print-only {
						display: none;
					}
					@media print {
						.no-print {
							display: none !important;
						}
						.print-only {
							display: block;
						}
						.print-container {
							padding: 1rem;
						}
						.card-print {
							box-shadow: none !important;
							border: none !important;
						}
						.print-service-col {
							max-width: 200px;
							white-space: normal !important;
						}
						.print-notes-col {
							max-width: 200px;
							white-space: normal !important;
						}
						.print-text-wrap {
							word-break: break-word;
							white-space: normal !important;
						}
					}

					@media (max-width: 640px) {
						.overflow-x-auto {
							overflow-x: auto;
						}

						.summary-grid {
							grid-template-columns: 1fr 1fr !important;
						}
					}
				`}</style>
			</div>
		</ErrorBoundary>
	);
}
