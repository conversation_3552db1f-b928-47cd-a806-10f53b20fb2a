/*
  Warnings:

  - The primary key for the `Employee` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The `id` column on the `Employee` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The primary key for the `Vehicle` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The `id` column on the `Vehicle` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - Made the column `contactInfo` on table `Employee` required. This step will fail if there are existing NULL values in that column.
  - Made the column `licensePlate` on table `Vehicle` required. This step will fail if there are existing NULL values in that column.
  - Made the column `ownerName` on table `Vehicle` required. This step will fail if there are existing NULL values in that column.
  - Made the column `ownerContact` on table `Vehicle` required. This step will fail if there are existing NULL values in that column.

*/
-- AlterTable
ALTER TABLE "Employee" DROP CONSTRAINT "Employee_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" SERIAL NOT NULL,
ALTER COLUMN "contactInfo" SET NOT NULL,
ADD CONSTRAINT "Employee_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Vehicle" DROP CONSTRAINT "Vehicle_pkey",
DROP COLUMN "id",
ADD COLUMN     "id" SERIAL NOT NULL,
ALTER COLUMN "licensePlate" SET NOT NULL,
ALTER COLUMN "ownerName" SET NOT NULL,
ALTER COLUMN "ownerContact" SET NOT NULL,
ADD CONSTRAINT "Vehicle_pkey" PRIMARY KEY ("id");
