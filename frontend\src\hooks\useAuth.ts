import { useEffect, useState, useCallback } from 'react';
import { User, Session, AuthError } from '@supabase/supabase-js';
import { supabase } from '../lib/supabase';

interface AuthState {
	user: User | null;
	session: Session | null;
	loading: boolean;
	error: string | null;
}

interface AuthActions {
	signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>;
	signUp: (email: string, password: string, metadata?: Record<string, any>) => Promise<{ error: AuthError | null }>;
	signOut: () => Promise<{ error: AuthError | null }>;
	resetPassword: (email: string) => Promise<{ error: AuthError | null }>;
	clearError: () => void;
}

export interface UseAuthReturn extends AuthState, AuthActions {}

/**
 * EMERGENCY SECURITY HOOK - Supabase Authentication
 * 
 * This hook manages the authentication state and provides methods
 * for signing in, signing up, and signing out users.
 * 
 * CRITICAL: This hook is part of the emergency security implementation
 */
export function useAuth(): UseAuthReturn {
	const [state, setState] = useState<AuthState>({
		user: null,
		session: null,
		loading: true,
		error: null,
	});

	// Clear error function
	const clearError = useCallback(() => {
		setState(prev => ({ ...prev, error: null }));
	}, []);

	// Sign in function
	const signIn = useCallback(async (email: string, password: string) => {
		setState(prev => ({ ...prev, loading: true, error: null }));
		
		try {
			const { data, error } = await supabase.auth.signInWithPassword({
				email,
				password,
			});

			if (error) {
				setState(prev => ({ 
					...prev, 
					loading: false, 
					error: error.message 
				}));
				return { error };
			}

			// State will be updated by the auth state change listener
			setState(prev => ({ ...prev, loading: false }));
			return { error: null };
		} catch (err: any) {
			const errorMessage = err.message || 'An unexpected error occurred during sign in';
			setState(prev => ({ 
				...prev, 
				loading: false, 
				error: errorMessage 
			}));
			return { error: { message: errorMessage } as AuthError };
		}
	}, []);

	// Sign up function
	const signUp = useCallback(async (email: string, password: string, metadata?: Record<string, any>) => {
		setState(prev => ({ ...prev, loading: true, error: null }));
		
		try {
			const { data, error } = await supabase.auth.signUp({
				email,
				password,
				options: {
					data: metadata || {},
				},
			});

			if (error) {
				setState(prev => ({ 
					...prev, 
					loading: false, 
					error: error.message 
				}));
				return { error };
			}

			setState(prev => ({ ...prev, loading: false }));
			return { error: null };
		} catch (err: any) {
			const errorMessage = err.message || 'An unexpected error occurred during sign up';
			setState(prev => ({ 
				...prev, 
				loading: false, 
				error: errorMessage 
			}));
			return { error: { message: errorMessage } as AuthError };
		}
	}, []);

	// Sign out function
	const signOut = useCallback(async () => {
		setState(prev => ({ ...prev, loading: true, error: null }));
		
		try {
			const { error } = await supabase.auth.signOut();

			if (error) {
				setState(prev => ({ 
					...prev, 
					loading: false, 
					error: error.message 
				}));
				return { error };
			}

			// State will be updated by the auth state change listener
			return { error: null };
		} catch (err: any) {
			const errorMessage = err.message || 'An unexpected error occurred during sign out';
			setState(prev => ({ 
				...prev, 
				loading: false, 
				error: errorMessage 
			}));
			return { error: { message: errorMessage } as AuthError };
		}
	}, []);

	// Reset password function
	const resetPassword = useCallback(async (email: string) => {
		setState(prev => ({ ...prev, loading: true, error: null }));
		
		try {
			const { error } = await supabase.auth.resetPasswordForEmail(email, {
				redirectTo: `${window.location.origin}/reset-password`,
			});

			if (error) {
				setState(prev => ({ 
					...prev, 
					loading: false, 
					error: error.message 
				}));
				return { error };
			}

			setState(prev => ({ ...prev, loading: false }));
			return { error: null };
		} catch (err: any) {
			const errorMessage = err.message || 'An unexpected error occurred during password reset';
			setState(prev => ({ 
				...prev, 
				loading: false, 
				error: errorMessage 
			}));
			return { error: { message: errorMessage } as AuthError };
		}
	}, []);

	// Initialize auth state and listen for changes
	useEffect(() => {
		let mounted = true;

		// Get initial session
		const getInitialSession = async () => {
			try {
				const { data: { session }, error } = await supabase.auth.getSession();
				
				if (mounted) {
					if (error) {
						console.error('Error getting initial session:', error);
						setState(prev => ({ 
							...prev, 
							loading: false, 
							error: error.message 
						}));
					} else {
						setState(prev => ({
							...prev,
							session,
							user: session?.user ?? null,
							loading: false,
						}));
					}
				}
			} catch (err: any) {
				if (mounted) {
					console.error('Error in getInitialSession:', err);
					setState(prev => ({ 
						...prev, 
						loading: false, 
						error: err.message || 'Failed to initialize authentication' 
					}));
				}
			}
		};

		getInitialSession();

		// Listen for auth changes
		const { data: { subscription } } = supabase.auth.onAuthStateChange(
			async (event, session) => {
				if (mounted) {
					console.log('🔐 Auth state changed:', event, session?.user?.email);
					
					setState(prev => ({
						...prev,
						session,
						user: session?.user ?? null,
						loading: false,
					}));

					// Clear any previous errors on successful auth change
					if (session && event === 'SIGNED_IN') {
						setState(prev => ({ ...prev, error: null }));
					}
				}
			}
		);

		return () => {
			mounted = false;
			subscription?.unsubscribe();
		};
	}, []);

	return {
		...state,
		signIn,
		signUp,
		signOut,
		resetPassword,
		clearError,
	};
}
