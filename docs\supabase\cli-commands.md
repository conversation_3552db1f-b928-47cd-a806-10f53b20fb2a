# Supabase CLI Commands Reference

This document provides a reference for all Supabase CLI commands implemented in
the WorkHub application.

## Command Categories

1. [Authentication Commands](#authentication-commands)
2. [Project Commands](#project-commands)
3. [Database Commands](#database-commands)
4. [Configuration Commands](#configuration-commands)
5. [Database Switching Commands](#database-switching-commands)
6. [Advanced Usage](#advanced-usage)

## Authentication Commands

### Login to Supabase

```bash
npm run supabase:login
```

Authenticates with Supa<PERSON> by opening a browser window. Your access token is
stored securely.

## Project Commands

### Link to a Supabase Project

```bash
npm run supabase:link
```

Links your local project to a Supabase project. You'll be prompted for:

- Supabase project ID
- Database password
- Service role key

This saves configuration to `supabase/config.json` and updates your `.env` file.

## Database Commands

### Pull Database Schema

```bash
npm run supabase:pull
```

Pulls the current database schema from Supabase into your Prisma schema using
<PERSON><PERSON><PERSON>'s introspection.

### Push Prisma Schema

```bash
npm run supabase:push
```

Pushes your Prisma schema to Supabase, applying schema changes directly to your
database.

### Run Migrations

```bash
npm run supabase:migrate
```

Runs Prisma migrations on your Supabase database, applying any pending
migrations.

### Seed Database

```bash
npm run supabase:seed
```

Seeds your Supabase database with initial data using your Prisma seed script.

## Configuration Commands

### View Configuration

```bash
npm run supabase:config
```

Displays your current Supabase configuration with sensitive information masked.

## Database Switching Commands

### Switch to Local PostgreSQL

```bash
npm run db:local
```

Switches to local PostgreSQL by updating `.env` with `USE_SUPABASE=false` and
local connection string.

### Switch to Supabase

```bash
npm run db:supabase
```

Switches to Supabase by updating `.env` with `USE_SUPABASE=true` and Supabase
connection string.

### Check Database Configuration

```bash
npm run db:status
```

Shows whether you're currently using local PostgreSQL or Supabase.

## Advanced Usage

### Direct Supabase CLI Access

```bash
npx supabase <command>
```

Example: `npx supabase help`

### Custom Script Options

```bash
node scripts/supabase-cli.js <command> [options]
```

### Environment Variables

The following environment variables are used:

- `DATABASE_URL`: PostgreSQL connection string
- `USE_SUPABASE`: Whether to use Supabase (`true`) or local PostgreSQL (`false`)
- `SUPABASE_URL`: Supabase project URL
- `SUPABASE_KEY`: Supabase anon key

### Configuration File

The `supabase/config.json` file stores your Supabase configuration:

```json
{
	"project_id": "your-project-id",
	"api": {
		"http_url": "https://your-project-id.supabase.co",
		"db_url": "postgresql://postgres:<EMAIL>:5432/postgres",
		"service_role_key": "your-service-role-key"
	}
}
```

**Important**: Add this file to your `.gitignore` to prevent committing
sensitive information.

## Troubleshooting

### Command Not Found

Make sure you're in the `backend` directory:

```bash
cd backend
npm run supabase:login
```

### Authentication Issues

Try logging in again:

```bash
npm run supabase:login
```

### Database Connection Issues

Check your configuration:

```bash
npm run supabase:config
```

### Schema Sync Issues

Try pulling the schema first, then pushing your changes:

```bash
npm run supabase:pull
npm run supabase:push
```
