'use client';

import {useCallback, useMemo} from 'react';
import {useAuthContext} from '../contexts/AuthContext';

/**
 * 🚨 EMERGENCY SECURITY: Authenticated API Hook
 *
 * This hook provides API functions that automatically include the current user's
 * JWT token in requests. It ensures that API calls are only made when the user
 * is authenticated and the token is available.
 */
export function useAuthenticatedApi() {
	const {session, user, loading} = useAuthContext();

	/**
	 * Make an authenticated API request
	 */
	const makeAuthenticatedRequest = useCallback(
		async <T = any>(url: string, options: RequestInit = {}): Promise<T> => {
			// Check if user is authenticated and token is available
			if (!user || !session?.access_token) {
				throw new Error('User not authenticated or token not available');
			}

			// Determine the API base URL
			const isDockerEnvironment =
				typeof window !== 'undefined' &&
				window.location.hostname !== 'localhost' &&
				window.location.hostname !== '127.0.0.1';

			const API_BASE_URL =
				process.env.NEXT_PUBLIC_API_BASE_URL ||
				(isDockerEnvironment
					? 'http://backend:3001/api'
					: 'http://localhost:3001/api');

			// Prepare headers with authentication
			const headers: Record<string, string> = {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${session.access_token}`,
			};

			// Merge with any additional headers from options
			if (options.headers) {
				Object.assign(headers, options.headers);
			}

			const requestOptions: RequestInit = {
				...options,
				headers,
			};

			// Make the request
			const fullUrl = url.startsWith('http') ? url : `${API_BASE_URL}${url}`;

			if (process.env.NODE_ENV !== 'production') {
				console.debug(
					`🔐 Authenticated API Request: ${
						requestOptions.method || 'GET'
					} ${fullUrl}`
				);
			}

			const response = await fetch(fullUrl, requestOptions);

			if (!response.ok) {
				let errorBody;
				try {
					errorBody = await response.json();
				} catch (e) {
					errorBody = {
						message: response.statusText || `HTTP error ${response.status}`,
					};
				}

				console.error(
					`🔐 Authenticated API Error ${response.status} at ${fullUrl}:`,
					errorBody
				);

				const detailedMessage =
					errorBody?.error?.message ||
					errorBody?.message ||
					'Unknown server error';

				throw new Error(
					`API request to ${fullUrl} failed with status ${response.status}: ${detailedMessage}`
				);
			}

			if (response.status === 204) {
				return null as T;
			}

			const data = await response.json();

			if (process.env.NODE_ENV !== 'production') {
				console.debug(`🔐 Authenticated API Response from ${fullUrl}:`, data);
			}

			return data as T;
		},
		[session?.access_token, user]
	);

	/**
	 * API methods that automatically include authentication
	 */
	const api = {
		// GET request
		get: useCallback(
			<T = any>(url: string): Promise<T> => {
				return makeAuthenticatedRequest<T>(url, {method: 'GET'});
			},
			[makeAuthenticatedRequest]
		),

		// POST request
		post: useCallback(
			<T = any>(url: string, data?: any): Promise<T> => {
				return makeAuthenticatedRequest<T>(url, {
					method: 'POST',
					body: data ? JSON.stringify(data) : undefined,
				});
			},
			[makeAuthenticatedRequest]
		),

		// PUT request
		put: useCallback(
			<T = any>(url: string, data?: any): Promise<T> => {
				return makeAuthenticatedRequest<T>(url, {
					method: 'PUT',
					body: data ? JSON.stringify(data) : undefined,
				});
			},
			[makeAuthenticatedRequest]
		),

		// DELETE request
		delete: useCallback(
			<T = any>(url: string): Promise<T> => {
				return makeAuthenticatedRequest<T>(url, {method: 'DELETE'});
			},
			[makeAuthenticatedRequest]
		),
	};

	return {
		api,
		isAuthenticated: !!user && !!session?.access_token,
		isLoading: loading,
		makeAuthenticatedRequest,
	};
}

/**
 * Specific API functions using the authenticated API hook
 */
export function useApiMethods() {
	const {api, isAuthenticated, isLoading} = useAuthenticatedApi();

	// Memoize API methods to prevent infinite re-renders
	const apiMethods = useMemo(
		() => ({
			// Vehicle API
			getVehicles: () => api.get('/vehicles'),
			getVehicleById: (id: number) => api.get(`/vehicles/${id}`),
			createVehicle: (data: any) => api.post('/vehicles', data),
			updateVehicle: (id: number, data: any) =>
				api.put(`/vehicles/${id}`, data),
			deleteVehicle: (id: number) => api.delete(`/vehicles/${id}`),

			// Employee API
			getEmployees: () => api.get('/employees'),
			getEmployeeById: (id: number) => api.get(`/employees/${id}`),
			createEmployee: (data: any) => api.post('/employees', data),
			updateEmployee: (id: number, data: any) =>
				api.put(`/employees/${id}`, data),
			deleteEmployee: (id: number) => api.delete(`/employees/${id}`),

			// Delegations API
			getDelegations: () => api.get('/delegations'),

			// Tasks API
			getTasks: () => api.get('/tasks'),

			// Admin API
			getAdminDiagnostics: () => api.get('/admin/diagnostics'),
		}),
		[api]
	);

	return {
		...apiMethods,
		// Status
		isAuthenticated,
		isLoading,
	};
}
