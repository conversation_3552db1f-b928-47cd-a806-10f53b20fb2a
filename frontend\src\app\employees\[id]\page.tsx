'use client';

import React, {useEffect, useState, useCallback} from 'react';
import {useRouter, useParams} from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {getEmployeeById, deleteEmployee, getVehicleById} from '@/lib/store';
import type {Employee, Vehicle} from '@/lib/types';
import {PageHeader} from '@/components/ui/PageHeader';
import {ActionButton} from '@/components/ui/action-button';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardDescription,
	CardFooter,
} from '@/components/ui/card';
import {Alert, AlertDescription, AlertTitle} from '@/components/ui/alert';
import {Separator} from '@/components/ui/separator';
import {Badge} from '@/components/ui/badge';
import {
	UsersRound,
	Edit,
	Trash2,
	Mail,
	Phone,
	Briefcase,
	Building,
	CalendarDays,
	ShieldCheck,
	UserCircle2,
	<PERSON><PERSON>,
	<PERSON>,
	MapPin,
	Car,
	CircleHelp,
	Alert<PERSON>riangle,
	Refresh<PERSON><PERSON>,
	ArrowLeft,
	Info,
} from 'lucide-react';
import {useToast} from '@/hooks/use-toast';
import {cn} from '@/lib/utils';
import {format, parseISO} from 'date-fns';
import {
	DataLoader,
	SkeletonLoader,
	ErrorDisplay,
} from '@/components/ui/loading';
import {Button} from '@/components/ui/button';
import DriverInformation from '@/components/drivers/DriverInformation';

const getStatusColor = (status?: Employee['status']) => {
	if (!status) return 'bg-gray-500/20 text-gray-700 border-gray-500/30';
	switch (status) {
		case 'Active':
			return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
		case 'On Leave':
			return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
		case 'Terminated':
			return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
		default:
			return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
	}
};

const getAvailabilityColor = (availability?: Employee['availability']) => {
	if (!availability) return 'bg-gray-500/20 text-gray-700 border-gray-500/30';
	switch (availability) {
		case 'On_Shift':
			return 'bg-green-500/20 text-green-700 border-green-500/30 dark:text-green-400 dark:bg-green-500/10 dark:border-green-500/20';
		case 'Off_Shift':
			return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
		case 'On_Break':
			return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30 dark:text-yellow-400 dark:bg-yellow-500/10 dark:border-yellow-500/20';
		case 'Busy':
			return 'bg-red-500/20 text-red-700 border-red-500/30 dark:text-red-400 dark:bg-red-500/10 dark:border-red-500/20';
		default:
			return 'bg-gray-500/20 text-gray-700 border-gray-500/30 dark:text-gray-400 dark:bg-gray-500/10 dark:border-gray-500/20';
	}
};

const EmployeeDetailPage = () => {
	const router = useRouter();
	const params = useParams();
	const {toast} = useToast();

	const employeeIdStr = params.id as string;
	const employeeId = parseInt(employeeIdStr, 10);

	const [employee, setEmployee] = useState<Employee | null>(null);
	const [assignedVehicle, setAssignedVehicle] = useState<Vehicle | null>(null);
	const [isLoadingData, setIsLoadingData] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [isDeleting, setIsDeleting] = useState(false);

	const fetchEmployeeDetails = useCallback(async () => {
		if (isNaN(employeeId)) {
			setError('Invalid employee ID.');
			setIsLoadingData(false);
			return;
		}
		setIsLoadingData(true);
		setError(null);
		try {
			const empData = await getEmployeeById(employeeId);
			if (empData) {
				setEmployee(empData);
				if (empData.role === 'driver' && empData.vehicleId) {
					try {
						const vehicleData = await getVehicleById(Number(empData.vehicleId));
						setAssignedVehicle(vehicleData || null);
					} catch (vehicleError) {
						console.warn('Failed to fetch assigned vehicle:', vehicleError);
						setAssignedVehicle(null);
					}
				}
			} else {
				setError('Employee not found.');
			}
		} catch (err: any) {
			console.error('Failed to fetch employee details:', err);
			setError(err.message || 'Failed to load employee data.');
		} finally {
			setIsLoadingData(false);
		}
	}, [employeeId]);

	useEffect(() => {
		if (employeeIdStr) fetchEmployeeDetails();
	}, [employeeIdStr, fetchEmployeeDetails]);

	const handleDelete = async () => {
		if (isNaN(employeeId) || !employee) {
			toast({
				title: 'Error',
				description: 'Invalid employee ID or employee data missing.',
				variant: 'destructive',
			});
			return;
		}

		// Enhanced confirmation dialog with employee name
		const employeeName = employee.name || employee.fullName || 'Employee';
		if (
			!window.confirm(
				`Are you sure you want to permanently delete ${employeeName}?\n\nThis action cannot be undone and will remove all employee data from the system.`
			)
		) {
			return;
		}

		setIsDeleting(true);
		setError(null);

		try {
			console.log(`Attempting to delete employee with ID: ${employeeId}`);
			await deleteEmployee(employeeId);

			toast({
				title: 'Employee Deleted Successfully',
				description: `${employeeName} has been permanently removed from the system.`,
				variant: 'default',
			});

			// Navigate back to employees list
			router.push('/employees');
		} catch (err: any) {
			console.error('Failed to delete employee:', err);

			// Enhanced error message based on error type
			let errorMessage = 'Could not delete employee. Please try again.';

			if (err.message?.includes('Network error')) {
				errorMessage =
					'Network error. Please check your connection and try again.';
			} else if (err.message?.includes('404')) {
				errorMessage = 'Employee not found or already deleted.';
			} else if (err.message?.includes('403')) {
				errorMessage = 'You do not have permission to delete this employee.';
			} else if (err.message?.includes('500')) {
				errorMessage = 'Server error. Please contact support if this persists.';
			} else if (err.response?.data?.error) {
				errorMessage = err.response.data.error;
			} else if (err.message) {
				errorMessage = err.message;
			}

			setError(errorMessage);
			toast({
				title: 'Failed to Delete Employee',
				description: errorMessage,
				variant: 'destructive',
			});
		} finally {
			setIsDeleting(false);
		}
	};

	const DetailItem: React.FC<{
		icon: React.ElementType;
		label: string;
		value?: string | number | null | React.ReactNode;
		valueClassName?: string;
	}> = ({icon: Icon, label, value, valueClassName}) =>
		value || value === 0 ? (
			<div className='flex items-start space-x-3'>
				<Icon className='h-5 w-5 text-accent mr-3 mt-0.5 shrink-0' />
				<div>
					<p className='text-sm text-muted-foreground'>{label}</p>
					<p
						className={cn(
							'text-base font-semibold text-foreground',
							valueClassName
						)}>
						{value}
					</p>
				</div>
			</div>
		) : null;

	return (
		<div className='container mx-auto py-8 space-y-8'>
			<DataLoader
				isLoading={isLoadingData}
				error={error}
				data={employee}
				onRetry={fetchEmployeeDetails}
				loadingComponent={
					<div className='space-y-6'>
						<PageHeader title='Loading Employee...' icon={UsersRound} />
						<SkeletonLoader variant='card' count={1} />
					</div>
				}
				emptyComponent={
					<div className='text-center py-10'>
						<PageHeader title='Employee Not Found' icon={AlertTriangle} />
						<p className='mb-4'>The requested employee could not be found.</p>
						<ActionButton
							actionType='primary'
							onClick={() => router.push('/employees')}
							icon={<ArrowLeft className='h-4 w-4' />}>
							Back to Employees
						</ActionButton>
					</div>
				}>
				{(loadedEmployee) => {
					const employeeName =
						loadedEmployee.name || loadedEmployee.fullName || 'Employee';
					return (
						<>
							<PageHeader
								title={employeeName}
								description={`Details for Employee ID: ${loadedEmployee.id}`}
								icon={UsersRound}
							/>

							{error && (
								<Alert variant='destructive'>
									<CircleHelp className='h-5 w-5' />
									<AlertTitle>An Error Occurred</AlertTitle>
									<AlertDescription>{error}</AlertDescription>
								</Alert>
							)}

							<Card className='shadow-lg'>
								<CardHeader className='p-5'>
									<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
										<div className='flex items-center gap-4'>
											<div className='relative w-20 h-20 rounded-full overflow-hidden bg-muted flex items-center justify-center ring-2 ring-primary/30'>
												{loadedEmployee.profileImageUrl ? (
													<Image
														src={loadedEmployee.profileImageUrl}
														alt={employeeName}
														layout='fill'
														objectFit='cover'
														data-ai-hint='employee profile'
													/>
												) : (
													<UserCircle2 className='h-12 w-12 text-muted-foreground' />
												)}
											</div>
											<div>
												<CardTitle className='text-2xl font-bold text-primary'>
													{employeeName}
												</CardTitle>
												<CardDescription className='text-sm'>
													{loadedEmployee.position} -{' '}
													{loadedEmployee.department}
												</CardDescription>
												<div className='mt-1 flex items-center gap-2'>
													<Badge
														className={cn(
															'text-xs',
															getStatusColor(loadedEmployee.status)
														)}>
														{loadedEmployee.status}
													</Badge>
													{loadedEmployee.role === 'driver' &&
														loadedEmployee.availability && (
															<Badge
																className={cn(
																	'text-xs',
																	getAvailabilityColor(
																		loadedEmployee.availability
																	)
																)}>
																{loadedEmployee.availability?.replace('_', ' ')}
															</Badge>
														)}
												</div>
											</div>
										</div>
										<div className='flex flex-wrap gap-2 self-start sm:self-center'>
											<ActionButton
												actionType='tertiary'
												size='icon'
												title='Refresh Employee Data'
												onClick={fetchEmployeeDetails}
												disabled={isLoadingData}
												className='relative group'>
												<RefreshCw
													className={cn(
														'h-4 w-4',
														isLoadingData && 'animate-spin'
													)}
												/>
												<span className='sr-only'>
													{isLoadingData
														? 'Refreshing...'
														: 'Refresh employee data'}
												</span>
											</ActionButton>
											<ActionButton
												actionType='secondary'
												size='icon'
												asChild
												title='Edit Employee Details'
												className='relative group'>
												<Link
													href={`/employees/${loadedEmployee.id}/edit`}
													className='inline-flex items-center justify-center'>
													<Edit className='h-4 w-4' />
													<span className='sr-only'>Edit {employeeName}</span>
												</Link>
											</ActionButton>
											<ActionButton
												actionType='danger'
												size='icon'
												title={`Delete ${employeeName}`}
												onClick={handleDelete}
												isLoading={isDeleting}
												disabled={isDeleting}
												className='relative group'
												loadingText=''>
												<Trash2 className='h-4 w-4' />
												<span className='sr-only'>
													{isDeleting
														? 'Deleting...'
														: `Delete ${employeeName}`}
												</span>
											</ActionButton>
										</div>
									</div>
								</CardHeader>
								<CardContent className='mt-2 space-y-8 p-5'>
									<section>
										<h3 className='text-xl font-semibold mb-3 text-primary flex items-center'>
											<UserCircle2 className='mr-2 h-5 w-5 text-accent' />{' '}
											Contact & Employment
										</h3>
										<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-4'>
											<DetailItem
												icon={Mail}
												label='Email / Primary Contact'
												value={loadedEmployee.contactEmail}
											/>
											{loadedEmployee.contactMobile && (
												<DetailItem
													icon={Phone}
													label='Mobile'
													value={loadedEmployee.contactMobile}
												/>
											)}
											{loadedEmployee.contactPhone &&
												!loadedEmployee.contactMobile && (
													<DetailItem
														icon={Phone}
														label='Phone'
														value={loadedEmployee.contactPhone}
													/>
												)}
											<DetailItem
												icon={CalendarDays}
												label='Hire Date'
												value={
													loadedEmployee.hireDate
														? format(
																parseISO(loadedEmployee.hireDate),
																'MMMM d, yyyy'
														  )
														: 'N/A'
												}
											/>
											<DetailItem
												icon={Briefcase}
												label='Role'
												value={
													loadedEmployee.role
														? loadedEmployee.role.charAt(0).toUpperCase() +
														  loadedEmployee.role.slice(1).replace('_', ' ')
														: 'N/A'
												}
											/>
											<DetailItem
												icon={Building}
												label='Department'
												value={loadedEmployee.department}
											/>
											<DetailItem
												icon={ShieldCheck}
												label='Employee ID (System)'
												value={loadedEmployee.id.toString()}
											/>
										</div>
									</section>

									{(loadedEmployee.role === 'driver' ||
										(loadedEmployee.skills &&
											loadedEmployee.skills.length > 0) ||
										loadedEmployee.shiftSchedule ||
										(loadedEmployee.generalAssignments &&
											loadedEmployee.generalAssignments.length > 0)) && (
										<Separator className='my-4' />
									)}

									{loadedEmployee.role === 'driver' && (
										<section>
											<h3 className='text-xl font-semibold mb-3 text-primary flex items-center'>
												<Car className='mr-2 h-5 w-5 text-accent' /> Driver
												Information
											</h3>
											<DriverInformation employee={loadedEmployee} />
										</section>
									)}

									{(loadedEmployee.skills &&
										loadedEmployee.skills.length > 0) ||
									loadedEmployee.shiftSchedule ||
									(loadedEmployee.generalAssignments &&
										loadedEmployee.generalAssignments.length > 0) ? (
										<section>
											<h3 className='text-xl font-semibold mb-3 text-primary flex items-center'>
												<Wrench className='mr-2 h-5 w-5 text-accent' /> Work
												Details
											</h3>
											<div className='grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4'>
												{loadedEmployee.skills &&
													loadedEmployee.skills.length > 0 && (
														<DetailItem
															icon={Wrench}
															label='Skills'
															value={loadedEmployee.skills.join(', ')}
														/>
													)}
												<DetailItem
													icon={Clock}
													label='Shift Schedule'
													value={loadedEmployee.shiftSchedule}
												/>
												{loadedEmployee.generalAssignments &&
													loadedEmployee.generalAssignments.length > 0 && (
														<DetailItem
															icon={Briefcase}
															label='General Assignments'
															value={loadedEmployee.generalAssignments.join(
																', '
															)}
														/>
													)}
											</div>
										</section>
									) : null}

									{loadedEmployee.notes && (
										<>
											<Separator className='my-4' />
											<section>
												<h3 className='text-xl font-semibold mb-2 text-primary flex items-center'>
													<Info className='mr-2 h-5 w-5 text-accent' /> Notes
												</h3>
												<p className='text-sm text-foreground whitespace-pre-wrap bg-muted/50 p-3 rounded-md'>
													{loadedEmployee.notes}
												</p>
											</section>
										</>
									)}
								</CardContent>
								<CardFooter className='text-xs text-muted-foreground p-5 pt-4 border-t'>
									Registered:{' '}
									{new Date(loadedEmployee.createdAt).toLocaleString()} | Last
									updated: {new Date(loadedEmployee.updatedAt).toLocaleString()}
								</CardFooter>
							</Card>
						</>
					);
				}}
			</DataLoader>
		</div>
	);
};

export default EmployeeDetailPage;
