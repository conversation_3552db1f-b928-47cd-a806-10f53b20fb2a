/**
 * Test script for Supabase real-time integration with Socket.io
 * 
 * This script:
 * 1. Connects to Supabase
 * 2. Sets up real-time subscriptions
 * 3. Simulates database changes
 * 4. Verifies that events are properly emitted to Socket.io
 * 
 * Usage:
 * node scripts/test-supabase-realtime.js
 */

import dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import { Server } from 'socket.io';
import http from 'http';

// Load environment variables
dotenv.config();

// Create a simple HTTP server for Socket.io
const server = http.createServer();
const io = new Server(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
  }
});

// Socket.io event names
const SOCKET_EVENTS = {
  VEHICLE_UPDATED: 'vehicle:updated',
  VEHICLE_CREATED: 'vehicle:created',
  VEHICLE_DELETED: 'vehicle:deleted',
  EMPLOYEE_UPDATED: 'employee:updated',
  EMPLOYEE_CREATED: 'employee:created',
  EMPLOYEE_DELETED: 'employee:deleted',
  REFRESH_VEHICLES: 'refresh:vehicles',
  REFRESH_EMPLOYEES: 'refresh:employees',
};

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: SUPABASE_URL and SUPABASE_KEY must be set in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Set up Socket.io connection handler
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);
  
  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

// Start the server
const PORT = 3099; // Use a different port than your main server
server.listen(PORT, () => {
  console.log(`Test server running on http://localhost:${PORT}`);
  console.log('Connect to this server with a Socket.io client to see real-time events');
  
  // Set up Supabase real-time subscriptions
  setupRealtimeSubscriptions();
});

// Set up Supabase real-time subscriptions
function setupRealtimeSubscriptions() {
  console.log('Setting up Supabase real-time subscriptions...');
  
  try {
    // Subscribe to vehicle changes
    const vehiclesChannel = supabase
      .channel('vehicles-changes')
      .on(
        'postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'vehicles' },
        (payload) => {
          console.log('Vehicle created:', payload.new.id);
          io.emit(SOCKET_EVENTS.VEHICLE_CREATED, payload.new);
          io.emit(SOCKET_EVENTS.REFRESH_VEHICLES);
        }
      )
      .on(
        'postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'vehicles' },
        (payload) => {
          console.log('Vehicle updated:', payload.new.id);
          io.emit(SOCKET_EVENTS.VEHICLE_UPDATED, payload.new);
          io.emit(SOCKET_EVENTS.REFRESH_VEHICLES);
        }
      )
      .on(
        'postgres_changes',
        { event: 'DELETE', schema: 'public', table: 'vehicles' },
        (payload) => {
          console.log('Vehicle deleted:', payload.old.id);
          io.emit(SOCKET_EVENTS.VEHICLE_DELETED, payload.old);
          io.emit(SOCKET_EVENTS.REFRESH_VEHICLES);
        }
      )
      .subscribe();
    
    // Subscribe to employee changes
    const employeesChannel = supabase
      .channel('employees-changes')
      .on(
        'postgres_changes',
        { event: 'INSERT', schema: 'public', table: 'employees' },
        (payload) => {
          console.log('Employee created:', payload.new.id);
          io.emit(SOCKET_EVENTS.EMPLOYEE_CREATED, payload.new);
          io.emit(SOCKET_EVENTS.REFRESH_EMPLOYEES);
        }
      )
      .on(
        'postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'employees' },
        (payload) => {
          console.log('Employee updated:', payload.new.id);
          io.emit(SOCKET_EVENTS.EMPLOYEE_UPDATED, payload.new);
          io.emit(SOCKET_EVENTS.REFRESH_EMPLOYEES);
        }
      )
      .on(
        'postgres_changes',
        { event: 'DELETE', schema: 'public', table: 'employees' },
        (payload) => {
          console.log('Employee deleted:', payload.old.id);
          io.emit(SOCKET_EVENTS.EMPLOYEE_DELETED, payload.old);
          io.emit(SOCKET_EVENTS.REFRESH_EMPLOYEES);
        }
      )
      .subscribe();
    
    console.log('Supabase real-time subscriptions set up successfully');
    console.log('Waiting for database changes...');
    console.log('Press Ctrl+C to exit');
    
  } catch (error) {
    console.error('Error setting up Supabase real-time subscriptions:', error);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('Shutting down...');
  io.close();
  server.close();
  process.exit(0);
});
