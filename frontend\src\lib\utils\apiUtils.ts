/**
 * Utility functions for API calls with retry logic
 */

/**
 * Configuration options for retry logic
 */
export interface RetryOptions {
  /** Maximum number of retry attempts */
  maxRetries?: number;
  /** Initial delay in milliseconds */
  initialDelay?: number;
  /** Maximum delay in milliseconds */
  maxDelay?: number;
  /** Backoff factor (how quickly the delay increases) */
  backoffFactor?: number;
  /** Function to determine if a retry should be attempted based on the error */
  shouldRetry?: (error: any) => boolean;
}

/**
 * Default retry options
 */
const defaultRetryOptions: RetryOptions = {
  maxRetries: 3,
  initialDelay: 300,
  maxDelay: 5000,
  backoffFactor: 2,
  shouldRetry: (error) => {
    // Retry on network errors or 5xx server errors
    if (!error.response) {
      return true; // Network error
    }
    return error.response.status >= 500 && error.response.status < 600;
  }
};

/**
 * Sleep for a specified duration
 * @param ms Milliseconds to sleep
 */
const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Calculate the delay for the next retry attempt using exponential backoff
 * @param attempt Current attempt number (0-based)
 * @param options Retry options
 */
export const calculateBackoffDelay = (
  attempt: number,
  options: RetryOptions = defaultRetryOptions
): number => {
  const { initialDelay = 300, backoffFactor = 2, maxDelay = 5000 } = options;
  
  // Calculate exponential backoff: initialDelay * (backoffFactor ^ attempt)
  const delay = initialDelay * Math.pow(backoffFactor, attempt);
  
  // Add some randomness to prevent all clients retrying simultaneously
  const jitter = Math.random() * 100;
  
  // Ensure the delay doesn't exceed the maximum
  return Math.min(delay + jitter, maxDelay);
};

/**
 * Execute a function with retry logic using exponential backoff
 * @param fn Function to execute
 * @param options Retry options
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: RetryOptions = defaultRetryOptions
): Promise<T> {
  const { 
    maxRetries = 3, 
    shouldRetry = defaultRetryOptions.shouldRetry 
  } = options;
  
  let attempt = 0;
  
  while (true) {
    try {
      return await fn();
    } catch (error) {
      attempt++;
      
      // If we've reached the maximum number of retries or shouldn't retry this error, throw
      if (attempt >= maxRetries || !shouldRetry(error)) {
        throw error;
      }
      
      // Calculate delay for this attempt
      const delay = calculateBackoffDelay(attempt, options);
      
      // Log retry attempt
      console.warn(`API call failed, retrying (${attempt}/${maxRetries}) after ${delay}ms`, error);
      
      // Wait before retrying
      await sleep(delay);
    }
  }
}
