/**
 * Jest Workspace Configuration
 * 
 * This file helps VS Code Jest extension identify the correct Jest configuration
 * for each part of the monorepo.
 */

module.exports = {
  // This tells Je<PERSON> to look for configuration in each project directory
  projects: [
    '<rootDir>/frontend',
    '<rootDir>/backend',
  ],
  rootDir: '.',
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
  ],
};
