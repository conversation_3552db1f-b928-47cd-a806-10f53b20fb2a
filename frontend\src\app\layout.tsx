'use client';

import type {<PERSON>ada<PERSON>} from 'next';
// Removed: import { GeistSans } from 'geist/font/sans';
// Removed: import { Geist<PERSON><PERSON> } from 'geist/font/mono'; // This line was causing the error
import './globals.css';
import Navbar from '@/components/global/Navbar';
import {Toaster} from '@/components/ui/toaster';
import {ThemeProvider} from '@/components/theme-provider';
import Script from 'next/script';
import localFont from 'next/font/local';
import 'leaflet/dist/leaflet.css';
import {AuthProvider, ProtectedRoute} from '@/components/auth';
import {usePathname} from 'next/navigation';
import {ReactNode} from 'react';

// Metadata moved to individual pages since this is now a client component

/**
 * Protected Layout Wrapper Component
 *
 * This component determines which routes require authentication.
 * Public routes (like auth-test) are excluded from protection.
 */
function ProtectedLayoutWrapper({children}: {children: ReactNode}) {
	const pathname = usePathname();

	// Define public routes that don't require authentication
	const publicRoutes = ['/auth-test', '/supabase-diagnostics'];

	// Check if current route is public
	const isPublicRoute = publicRoutes.some((route) =>
		pathname.startsWith(route)
	);

	// If it's a public route, render without protection
	if (isPublicRoute) {
		return <>{children}</>;
	}

	// For all other routes, require authentication
	return (
		<ProtectedRoute requireEmailVerification={true}>{children}</ProtectedRoute>
	);
}

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang='en' className='h-full' suppressHydrationWarning>
			{/* Apply a default sans-serif font stack */}
			<head>
				{/*
				  TEMPORARY HOTFIX: Patches Array.prototype.findIndex
				  PURPOSE: To prevent 'findIndex is not a function' errors in production
				  RISK: Modifying native prototypes can have unintended side effects
				  REMOVAL PLAN: Must be removed once backend and frontend fixes are stable (Target: Sprint 24)
				*/}
				<Script src='/fix-findindex-error.js' strategy='beforeInteractive' />
			</head>
			<body className={`font-sans antialiased flex flex-col min-h-screen`}>
				<ThemeProvider
					attribute='class'
					defaultTheme='system'
					enableSystem
					disableTransitionOnChange>
					<AuthProvider>
						<ProtectedLayoutWrapper>
							<Navbar />
							<main className='flex-grow container mx-auto px-4 sm:px-6 lg:px-8 py-8'>
								{children}
							</main>
							<Toaster />
							<footer className='bg-card text-card-foreground py-4 text-center text-sm no-print border-t border-border'>
								<p>
									&copy; {new Date().getFullYear()} WorkHub. All rights
									reserved.
								</p>
							</footer>
						</ProtectedLayoutWrapper>
					</AuthProvider>
				</ThemeProvider>
			</body>
		</html>
	);
}
