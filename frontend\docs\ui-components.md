# WorkHub UI Component Standards

This document outlines the standardized UI components for the WorkHub application, providing guidelines for consistent usage across the application.

## Table of Contents

1. [Button Components](#button-components)
2. [Loading Components](#loading-components)
3. [Error Handling Components](#error-handling-components)
4. [Card Components](#card-components)

## Button Components

### ActionButton

The `ActionButton` component provides a standardized way to create buttons with consistent styling, icon placement, and loading states.

#### Import

```tsx
import { ActionButton } from '@/components/ui/action-button';
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `actionType` | `'primary' \| 'secondary' \| 'tertiary' \| 'danger'` | `'primary'` | The type of action this button represents |
| `icon` | `React.ReactNode` | - | Icon to display before the button text |
| `isLoading` | `boolean` | `false` | Whether the button is in a loading state |
| `loadingText` | `string` | - | Text to display when button is loading |
| `asChild` | `boolean` | `false` | Whether to render as a child component (for Link integration) |

#### Usage Examples

**Primary Action Button**

```tsx
<ActionButton 
  actionType="primary" 
  icon={<PlusCircle className="h-4 w-4" />}
>
  Add New Vehicle
</ActionButton>
```

**Secondary Action Button**

```tsx
<ActionButton 
  actionType="secondary" 
  icon={<Eye className="h-4 w-4" />}
>
  View Details
</ActionButton>
```

**Tertiary Action Button**

```tsx
<ActionButton 
  actionType="tertiary" 
  icon={<ArrowLeft className="h-4 w-4" />}
>
  Cancel
</ActionButton>
```

**Danger Action Button**

```tsx
<ActionButton 
  actionType="danger" 
  icon={<Trash className="h-4 w-4" />}
>
  Delete
</ActionButton>
```

**Loading State**

```tsx
<ActionButton 
  actionType="primary" 
  isLoading={isSubmitting} 
  loadingText="Saving..."
>
  Save Changes
</ActionButton>
```

**With Link**

```tsx
<ActionButton 
  actionType="primary" 
  icon={<PlusCircle className="h-4 w-4" />}
  asChild
>
  <Link href="/vehicles/new">Add New Vehicle</Link>
</ActionButton>
```

## Loading Components

### LoadingSpinner

The `LoadingSpinner` component provides a standardized way to display loading spinners.

#### Import

```tsx
import { LoadingSpinner } from '@/components/ui/loading';
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `size` | `'sm' \| 'md' \| 'lg' \| 'xl'` | `'md'` | Size of the spinner |
| `text` | `string` | - | Text to display below the spinner |
| `fullPage` | `boolean` | `false` | Whether to display as a full-page overlay |
| `className` | `string` | - | Custom CSS class names |

#### Usage Examples

**Simple Loading Spinner**

```tsx
<LoadingSpinner size="md" text="Loading data..." />
```

**Full Page Loading Overlay**

```tsx
<LoadingSpinner size="lg" text="Processing..." fullPage />
```

### SkeletonLoader

The `SkeletonLoader` component provides a standardized way to display skeleton loading states for different content types.

#### Import

```tsx
import { SkeletonLoader } from '@/components/ui/loading';
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `variant` | `'default' \| 'card' \| 'table' \| 'list' \| 'stats'` | `'default'` | Type of content to show a skeleton for |
| `count` | `number` | `1` | Number of skeleton items to display |
| `className` | `string` | - | Custom CSS class names |
| `testId` | `string` | `'loading-skeleton'` | Test ID for testing |

#### Usage Examples

**Card Skeleton**

```tsx
<SkeletonLoader variant="card" count={3} />
```

**Table Skeleton**

```tsx
<SkeletonLoader variant="table" count={5} />
```

**List Skeleton**

```tsx
<SkeletonLoader variant="list" count={3} />
```

**Stats Skeleton**

```tsx
<SkeletonLoader variant="stats" count={4} />
```

### DataLoader

The `DataLoader` component provides a standardized way to handle loading, error, and empty states for data fetching.

#### Import

```tsx
import { DataLoader } from '@/components/ui/loading';
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `isLoading` | `boolean` | - | Whether data is currently loading |
| `error` | `string \| null \| undefined` | - | Error message, if any |
| `data` | `T \| null \| undefined` | - | The data to render |
| `onRetry` | `() => void` | - | Function to retry loading data |
| `children` | `(data: T) => React.ReactNode` | - | Render function for the data |
| `loadingComponent` | `React.ReactNode` | - | Custom loading component |
| `errorComponent` | `React.ReactNode` | - | Custom error component |
| `emptyComponent` | `React.ReactNode` | - | Custom empty state component |
| `className` | `string` | - | Custom CSS class names |

#### Usage Example

```tsx
<DataLoader
  isLoading={isLoading}
  error={error}
  data={vehicles}
  onRetry={refetch}
  loadingComponent={<SkeletonLoader variant="card" count={3} />}
  emptyComponent={
    <div className="text-center py-8">
      <p className="text-muted-foreground mb-4">No vehicles found</p>
      <ActionButton 
        actionType="primary" 
        icon={<PlusCircle className="h-4 w-4" />}
        asChild
      >
        <Link href="/vehicles/new">Add Your First Vehicle</Link>
      </ActionButton>
    </div>
  }
>
  {(vehicles) => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {vehicles.map(vehicle => (
        <VehicleCard key={vehicle.id} vehicle={vehicle} />
      ))}
    </div>
  )}
</DataLoader>
```

## Error Handling Components

### ErrorDisplay

The `ErrorDisplay` component provides a standardized way to display error messages.

#### Import

```tsx
import { ErrorDisplay } from '@/components/ui/loading';
```

#### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `message` | `string` | - | Error message to display |
| `onRetry` | `() => void` | - | Function to retry the operation |
| `className` | `string` | - | Custom CSS class names |

#### Usage Example

```tsx
<ErrorDisplay 
  message="Failed to load vehicles. Please try again." 
  onRetry={refetchVehicles} 
/>
```

## Card Components

*Note: Card component standardization will be implemented in a future phase.*
