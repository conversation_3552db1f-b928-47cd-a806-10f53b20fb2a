'use client';

import React from 'react';
import type { Task } from '@/lib/types';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { isPast } from 'date-fns';

/**
 * Props for the SummaryStatCard component
 */
interface SummaryStatCardProps {
  /** Value to display (typically a number) */
  value: number | string;
  /** Label to display below the value */
  label: string;
  /** Additional CSS class for the card background */
  className?: string;
  /** CSS class for the label text color */
  textColor?: string;
  /** Optional column span for the card */
  colSpan?: 'col-span-1' | 'col-span-2' | 'col-span-3';
}

/**
 * A card component for displaying a summary statistic
 */
function SummaryStatCard({ 
  value, 
  label, 
  className, 
  textColor = "text-gray-500",
  colSpan
}: SummaryStatCardProps) {
  return (
    <Card className={cn("overflow-hidden", className, colSpan)}>
      <CardContent className="p-2 text-center">
        <p className="text-2xl font-semibold">{value}</p>
        <p className={cn("text-xs", textColor)}>{label}</p>
      </CardContent>
    </Card>
  );
}

/**
 * Props for the TaskSummary component
 */
interface TaskSummaryProps {
  /** Array of tasks to display summary statistics for */
  tasks: Task[];
  /** Additional CSS class names */
  className?: string;
}

/**
 * A component that displays summary statistics for tasks
 * 
 * @example
 * ```tsx
 * <TaskSummary tasks={filteredTasks} />
 * ```
 */
export function TaskSummary({ 
  tasks,
  className
}: TaskSummaryProps) {
  // Calculate statistics
  const totalTasks = tasks.length;
  
  // Count tasks by status
  const tasksByStatus = tasks.reduce((acc, task) => {
    acc[task.status] = (acc[task.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  // Count tasks by priority
  const tasksByPriority = tasks.reduce((acc, task) => {
    acc[task.priority] = (acc[task.priority] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  // Count overdue tasks (tasks with a deadline in the past that are not Completed or Cancelled)
  const overdueTasks = tasks.filter(task => 
    task.deadline && 
    isPast(new Date(task.deadline)) && 
    task.status !== 'Completed' && 
    task.status !== 'Cancelled'
  ).length;
  
  // Count unassigned tasks (tasks without an assignedEmployeeId that are not Completed or Cancelled)
  const unassignedTasks = tasks.filter(task => 
    !task.assignedEmployeeId && 
    task.status !== 'Completed' && 
    task.status !== 'Cancelled'
  ).length;
  
  // Get status distribution for visualization
  const statusOrder = ['Pending', 'Assigned', 'In_Progress', 'Completed', 'Cancelled'];
  const statusColors = {
    'Pending': 'bg-yellow-100 text-yellow-800',
    'Assigned': 'bg-blue-100 text-blue-800',
    'In_Progress': 'bg-purple-100 text-purple-800',
    'Completed': 'bg-green-100 text-green-800',
    'Cancelled': 'bg-gray-100 text-gray-800'
  };
  
  // Get priority distribution for visualization
  const priorityOrder = ['High', 'Medium', 'Low'];
  const priorityColors = {
    'High': 'bg-red-100 text-red-800',
    'Medium': 'bg-orange-100 text-orange-800',
    'Low': 'bg-blue-100 text-blue-800'
  };
  
  return (
    <div className={cn("mt-4 space-y-4", className)}>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-2 summary-grid">
        {/* Total Tasks Card */}
        <SummaryStatCard 
          value={totalTasks}
          label="Total Tasks"
          className="bg-gray-50"
          textColor="text-gray-500"
        />
        
        {/* Overdue Tasks Card */}
        <SummaryStatCard 
          value={overdueTasks}
          label="Overdue Tasks"
          className={cn(
            "bg-red-50",
            overdueTasks > 0 ? "border-red-200" : ""
          )}
          textColor="text-red-500"
        />
        
        {/* Unassigned Tasks Card */}
        <SummaryStatCard 
          value={unassignedTasks}
          label="Unassigned Tasks"
          className={cn(
            "bg-amber-50",
            unassignedTasks > 0 ? "border-amber-200" : ""
          )}
          textColor="text-amber-500"
        />
        
        {/* In Progress Tasks Card */}
        <SummaryStatCard 
          value={tasksByStatus['In_Progress'] || 0}
          label="In Progress"
          className="bg-purple-50"
          textColor="text-purple-500"
        />
        
        {/* Completed Tasks Card */}
        <SummaryStatCard 
          value={tasksByStatus['Completed'] || 0}
          label="Completed"
          className="bg-green-50"
          textColor="text-green-500"
        />
      </div>
      
      {/* Status Distribution */}
      <Card className="overflow-hidden">
        <CardContent className="p-4">
          <h3 className="text-sm font-semibold mb-2">Status Distribution</h3>
          <div className="space-y-2">
            {statusOrder.map(status => (
              <div key={status} className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span>{status}</span>
                  <span>{tasksByStatus[status] || 0} tasks ({totalTasks ? Math.round((tasksByStatus[status] || 0) / totalTasks * 100) : 0}%)</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={cn("h-2 rounded-full", statusColors[status as keyof typeof statusColors])}
                    style={{ width: `${totalTasks ? (tasksByStatus[status] || 0) / totalTasks * 100 : 0}%` }}
                    aria-label={`${status}: ${tasksByStatus[status] || 0} tasks`}
                  />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      
      {/* Priority Distribution */}
      <Card className="overflow-hidden">
        <CardContent className="p-4">
          <h3 className="text-sm font-semibold mb-2">Priority Distribution</h3>
          <div className="flex flex-wrap gap-2 mt-1">
            {priorityOrder.map(priority => (
              <div 
                key={priority} 
                className={cn(
                  "text-xs px-3 py-1 rounded-full", 
                  priorityColors[priority as keyof typeof priorityColors]
                )}
              >
                {priority}: {tasksByPriority[priority] || 0}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
