generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model UserProfile {
  id         String    @id @db.Uuid // Maps to auth.users.id directly
  role       UserRole  @default(USER)
  employeeId Int?      @unique @map("employee_id") // Optional link to Employee record
  employee   Employee? @relation("UserProfileEmployee", fields: [employeeId], references: [id], onDelete: SetNull)
  isActive   Boolean   @default(true) @map("is_active")
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedAt  DateTime  @updatedAt @map("updated_at")

  @@map("user_profiles")
}

model Vehicle {
  id              Int             @id @default(autoincrement())
  make            String
  model           String
  year            Int
  vin             String          @unique
  licensePlate    String
  ownerName       String
  ownerContact    String
  color           String?
  initialOdometer Int?
  imageUrl        String?
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt
  assignedDriver  Employee?       @relation("DriverVehicle")
  serviceRecords  ServiceRecord[]
  tasks           Task[]
}

model Employee {
  id                 Int                   @id @default(autoincrement())
  name               String
  fullName           String?
  role               EmployeeRole
  employeeId         String                @unique
  contactInfo        String
  contactEmail       String?
  contactPhone       String?
  contactMobile      String?
  position           String?
  department         String?
  hireDate           DateTime?
  status             EmployeeStatus?
  availability       DriverAvailability?
  currentLocation    String?
  workingHours       String?
  assignedVehicleId  Int?                  @unique
  skills             String[]              @default([])
  shiftSchedule      String?
  generalAssignments String[]              @default([])
  notes              String?
  profileImageUrl    String?
  createdAt          DateTime              @default(now())
  updatedAt          DateTime              @updatedAt
  assignedVehicle    Vehicle?              @relation("DriverVehicle", fields: [assignedVehicleId], references: [id])
  statusHistory      EmployeeStatusEntry[]
  serviceRecords     ServiceRecord[]
  assignedTasks      Task[]                @relation("EmployeeTasks")
  userProfile        UserProfile?          @relation("UserProfileEmployee") // Link to user profile for RBAC
}

model EmployeeStatusEntry {
  id         String         @id @default(uuid())
  employeeId Int
  status     EmployeeStatus
  changedAt  DateTime       @default(now())
  reason     String?
  employee   Employee       @relation(fields: [employeeId], references: [id], onDelete: Cascade)
}

model ServiceRecord {
  id               String    @id @default(uuid())
  vehicleId        Int
  employeeId       Int?
  date             DateTime
  odometer         Int
  servicePerformed String[]
  notes            String?
  cost             Decimal?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  employee         Employee? @relation(fields: [employeeId], references: [id])
  vehicle          Vehicle   @relation(fields: [vehicleId], references: [id], onDelete: Cascade)
}

model Delegation {
  id                     String                  @id @default(uuid())
  eventName              String
  location               String
  durationFrom           DateTime
  durationTo             DateTime
  invitationFrom         String?
  invitationTo           String?
  flightArrivalId        String?                 @unique
  flightDepartureId      String?                 @unique
  status                 DelegationStatus
  notes                  String?
  imageUrl               String?
  createdAt              DateTime                @default(now())
  updatedAt              DateTime                @updatedAt
  delegates              Delegate[]
  flightArrivalDetails   FlightDetails?          @relation("DelegationArrivalFlight", fields: [flightArrivalId], references: [id])
  flightDepartureDetails FlightDetails?          @relation("DelegationDepartureFlight", fields: [flightDepartureId], references: [id])
  statusHistory          DelegationStatusEntry[]
}

model Delegate {
  id           String     @id @default(uuid())
  delegationId String
  name         String
  title        String
  notes        String?
  delegation   Delegation @relation(fields: [delegationId], references: [id], onDelete: Cascade)
}

model DelegationStatusEntry {
  id           String           @id @default(uuid())
  delegationId String
  status       DelegationStatus
  changedAt    DateTime         @default(now())
  reason       String?
  delegation   Delegation       @relation(fields: [delegationId], references: [id], onDelete: Cascade)
}

model FlightDetails {
  id                    String      @id @default(uuid())
  flightNumber          String
  dateTime              DateTime
  airport               String
  terminal              String?
  notes                 String?
  arrivalDelegationId   String?     @unique
  departureDelegationId String?     @unique
  arrivalDelegation     Delegation? @relation("DelegationArrivalFlight")
  departureDelegation   Delegation? @relation("DelegationDepartureFlight")
}

model Task {
  id                  String            @id @default(uuid())
  description         String
  location            String
  dateTime            DateTime
  estimatedDuration   Int
  requiredSkills      String[]          @default([])
  priority            TaskPriority
  deadline            DateTime?
  status              TaskStatus
  notes               String?
  vehicleId           Int?
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt
  assignedEmployeeIds String[]          @default([])
  subTasks            SubTask[]
  vehicle             Vehicle?          @relation(fields: [vehicleId], references: [id])
  statusHistory       TaskStatusEntry[]
  assignedEmployees   Employee[]        @relation("EmployeeTasks")
}

model SubTask {
  id        String  @id @default(uuid())
  taskId    String
  title     String
  completed Boolean @default(false)
  task      Task    @relation(fields: [taskId], references: [id], onDelete: Cascade)
}

model TaskStatusEntry {
  id        String     @id @default(uuid())
  taskId    String
  status    TaskStatus
  changedAt DateTime   @default(now())
  reason    String?
  task      Task       @relation(fields: [taskId], references: [id], onDelete: Cascade)
}

enum UserRole {
  SUPER_ADMIN
  ADMIN
  MANAGER
  USER
  READONLY
}

enum EmployeeRole {
  driver
  mechanic
  administrator
  office_staff
  manager
  service_advisor
  technician
  other
}

enum EmployeeStatus {
  Active
  On_Leave
  Terminated
  Inactive
}

enum DriverAvailability {
  On_Shift
  Off_Shift
  On_Break
  Busy
}

enum DelegationStatus {
  Planned
  Confirmed
  In_Progress
  Completed
  Cancelled
  No_details
}

enum TaskStatus {
  Pending
  Assigned
  In_Progress
  Completed
  Cancelled
}

enum TaskPriority {
  Low
  Medium
  High
}
