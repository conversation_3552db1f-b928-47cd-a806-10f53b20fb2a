# Date Handling Testing Guide

## Overview

This document outlines the testing strategy for the date handling utilities in the Car Life Tracker application. It provides guidance on how to effectively test date-related functionality to ensure robustness and reliability.

## Testing Approach

The testing approach for date utilities follows these principles:

1. **Comprehensive Coverage**: Test all utility functions with various inputs
2. **Edge Case Testing**: Test boundary conditions and error cases
3. **Isolation**: Test each function independently
4. **Integration**: Test how functions work together in real scenarios

## Unit Testing Date Utilities

### Test Categories

Each date utility function should be tested across these categories:

1. **Happy Path**: Test with valid, expected inputs
2. **Edge Cases**: Test with boundary values
3. **Error Cases**: Test with invalid inputs
4. **Type Variations**: Test with different input types (string, Date, undefined)

### Example Test Suite Structure

```typescript
// src/lib/utils/__tests__/dateUtils.test.ts
import {
  formatDateForInput,
  formatDateForDisplay,
  formatDateForApi,
  parseDateFromApi,
  isValidIsoDateString,
  isValidDateString,
  isDateAfter
} from '../dateUtils';

describe('Date Utility Functions', () => {
  // Test formatDateForInput
  describe('formatDateForInput', () => {
    it('should format a Date object for datetime-local input', () => {
      // Test implementation
    });

    it('should format an ISO string for datetime-local input', () => {
      // Test implementation
    });

    it('should return empty string for undefined input', () => {
      // Test implementation
    });

    it('should return empty string for invalid date input', () => {
      // Test implementation
    });
  });

  // Test other functions...
});
```

### Test Implementation Examples

#### Testing formatDateForInput

```typescript
it('should format a Date object for datetime-local input', () => {
  const date = new Date('2023-05-15T14:30:00Z');
  const result = formatDateForInput(date);
  expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/);
});

it('should format an ISO string for datetime-local input', () => {
  const dateString = '2023-05-15T14:30:00Z';
  const result = formatDateForInput(dateString);
  expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/);
});

it('should return empty string for undefined input', () => {
  const result = formatDateForInput(undefined);
  expect(result).toBe('');
});

it('should return empty string for invalid date input', () => {
  const result = formatDateForInput('not-a-date');
  expect(result).toBe('');
});
```

#### Testing isDateAfter

```typescript
it('should return true when first date is after second date', () => {
  const date1 = new Date('2023-05-15T14:30:00Z');
  const date2 = new Date('2023-05-14T14:30:00Z');
  const result = isDateAfter(date1, date2);
  expect(result).toBe(true);
});

it('should return false when first date is before second date', () => {
  const date1 = new Date('2023-05-14T14:30:00Z');
  const date2 = new Date('2023-05-15T14:30:00Z');
  const result = isDateAfter(date1, date2);
  expect(result).toBe(false);
});

it('should return false when dates are equal', () => {
  const date1 = new Date('2023-05-15T14:30:00Z');
  const date2 = new Date('2023-05-15T14:30:00Z');
  const result = isDateAfter(date1, date2);
  expect(result).toBe(false);
});

it('should handle string dates', () => {
  const date1 = '2023-05-15T14:30:00Z';
  const date2 = '2023-05-14T14:30:00Z';
  const result = isDateAfter(date1, date2);
  expect(result).toBe(true);
});

it('should return false when either date is undefined', () => {
  const date1 = new Date('2023-05-15T14:30:00Z');
  const result1 = isDateAfter(date1, undefined);
  const result2 = isDateAfter(undefined, date1);
  expect(result1).toBe(false);
  expect(result2).toBe(false);
});

it('should return false when either date is invalid', () => {
  const date1 = new Date('2023-05-15T14:30:00Z');
  const result1 = isDateAfter(date1, 'not-a-date');
  const result2 = isDateAfter('not-a-date', date1);
  expect(result1).toBe(false);
  expect(result2).toBe(false);
});
```

## Testing Form Validation

### Test Categories for Form Validation

1. **Valid Inputs**: Test that valid date inputs pass validation
2. **Invalid Formats**: Test that invalid date formats trigger appropriate errors
3. **Required Fields**: Test that required date fields cannot be empty
4. **Optional Fields**: Test that optional date fields can be empty
5. **Cross-Field Validation**: Test date range validation (e.g., end date after start date)

### Example Test Implementation

```typescript
import { TaskSchema } from '../schemas/taskSchemas';

describe('TaskSchema Date Validation', () => {
  it('should validate a valid task with valid dates', () => {
    const validTask = {
      description: 'Test task',
      location: 'Test location',
      dateTime: '2023-05-15T14:30',
      estimatedDuration: 60,
      // Other required fields...
    };
    
    const result = TaskSchema.safeParse(validTask);
    expect(result.success).toBe(true);
  });

  it('should reject invalid date format for dateTime', () => {
    const invalidTask = {
      description: 'Test task',
      location: 'Test location',
      dateTime: 'not-a-date',
      estimatedDuration: 60,
      // Other required fields...
    };
    
    const result = TaskSchema.safeParse(invalidTask);
    expect(result.success).toBe(false);
    if (!result.success) {
      const errors = result.error.format();
      expect(errors.dateTime?._errors).toContain(
        'Please enter a valid date and time in YYYY-MM-DD HH:MM format'
      );
    }
  });

  it('should reject deadline earlier than start date', () => {
    const invalidTask = {
      description: 'Test task',
      location: 'Test location',
      dateTime: '2023-05-15T14:30',
      deadline: '2023-05-14T14:30', // Earlier than dateTime
      estimatedDuration: 60,
      // Other required fields...
    };
    
    const result = TaskSchema.safeParse(invalidTask);
    expect(result.success).toBe(false);
    if (!result.success) {
      const errors = result.error.format();
      expect(errors.deadline?._errors).toContain(
        'Deadline cannot be earlier than the start date & time'
      );
    }
  });
});
```

## Integration Testing

Integration tests should verify that date handling works correctly in the context of forms and API interactions:

1. **Form Initialization**: Test that forms are initialized with correct date values
2. **Form Submission**: Test that dates are correctly formatted for API submission
3. **API Integration**: Test that dates are correctly parsed from API responses

### Example Integration Test

```typescript
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import TaskForm from '../components/tasks/TaskForm';
import { formatDateForInput } from '../lib/utils/dateUtils';

describe('TaskForm Integration', () => {
  it('should initialize with correct date values', () => {
    const initialData = {
      id: '1',
      description: 'Test task',
      location: 'Test location',
      dateTime: '2023-05-15T14:30:00Z',
      estimatedDuration: 60,
      // Other fields...
    };
    
    const onSubmit = jest.fn();
    render(<TaskForm onSubmit={onSubmit} initialData={initialData} isEditing={true} />);
    
    const dateTimeInput = screen.getByLabelText(/Start Date & Time/i);
    expect(dateTimeInput.value).toBe(formatDateForInput(initialData.dateTime));
  });

  it('should show validation error for invalid date', async () => {
    const onSubmit = jest.fn();
    render(<TaskForm onSubmit={onSubmit} />);
    
    const dateTimeInput = screen.getByLabelText(/Start Date & Time/i);
    fireEvent.change(dateTimeInput, { target: { value: 'invalid-date' } });
    
    const submitButton = screen.getByRole('button', { name: /Create Task/i });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/Please enter a valid date and time/i)).toBeInTheDocument();
    });
    
    expect(onSubmit).not.toHaveBeenCalled();
  });

  it('should format dates correctly on submission', async () => {
    const onSubmit = jest.fn();
    render(<TaskForm onSubmit={onSubmit} />);
    
    const dateTimeInput = screen.getByLabelText(/Start Date & Time/i);
    fireEvent.change(dateTimeInput, { target: { value: '2023-05-15T14:30' } });
    
    // Fill other required fields...
    
    const submitButton = screen.getByRole('button', { name: /Create Task/i });
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(onSubmit).toHaveBeenCalled();
    });
    
    const submittedData = onSubmit.mock.calls[0][0];
    expect(submittedData.dateTime).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/); // ISO format
  });
});
```

## Test Coverage Goals

Aim for the following coverage metrics for date-related code:

- **Statement Coverage**: >80%
- **Branch Coverage**: >80%
- **Function Coverage**: 100%
- **Line Coverage**: >80%

The uncovered lines are typically in error handling blocks that are difficult to test.

## Running Tests

### Running All Tests

```bash
npm test
```

### Running Tests with Coverage

```bash
npm run test:coverage
```

### Running Tests in Watch Mode

```bash
npm run test:watch
```

## Best Practices for Date Testing

1. **Use Fixed Dates**: Use fixed dates in tests to ensure consistent results
2. **Test Time Zone Handling**: Be aware of time zone issues in tests
3. **Mock Date Functions**: Consider mocking `Date` for predictable results
4. **Test Error Handling**: Ensure error cases are properly handled
5. **Test with Real-World Examples**: Use realistic date formats and values

## Conclusion

Thorough testing of date handling functionality is essential for ensuring a robust application. By following the testing strategies outlined in this guide, you can have confidence in the reliability of your date-related code.

---

*Document created: May 2024*  
*Last updated: May 2024*  
*Author: Development Team*