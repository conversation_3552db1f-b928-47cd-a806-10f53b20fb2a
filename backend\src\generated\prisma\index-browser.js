
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.7.0
 * Query Engine version: 3cff47a7f5d65c3ea74883f1d736e41d68ce91ed
 */
Prisma.prismaVersion = {
  client: "6.7.0",
  engine: "3cff47a7f5d65c3ea74883f1d736e41d68ce91ed"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserProfileScalarFieldEnum = {
  id: 'id',
  role: 'role',
  employeeId: 'employeeId',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.VehicleScalarFieldEnum = {
  id: 'id',
  make: 'make',
  model: 'model',
  year: 'year',
  vin: 'vin',
  licensePlate: 'licensePlate',
  ownerName: 'ownerName',
  ownerContact: 'ownerContact',
  color: 'color',
  initialOdometer: 'initialOdometer',
  imageUrl: 'imageUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EmployeeScalarFieldEnum = {
  id: 'id',
  name: 'name',
  fullName: 'fullName',
  role: 'role',
  employeeId: 'employeeId',
  contactInfo: 'contactInfo',
  contactEmail: 'contactEmail',
  contactPhone: 'contactPhone',
  contactMobile: 'contactMobile',
  position: 'position',
  department: 'department',
  hireDate: 'hireDate',
  status: 'status',
  availability: 'availability',
  currentLocation: 'currentLocation',
  workingHours: 'workingHours',
  assignedVehicleId: 'assignedVehicleId',
  skills: 'skills',
  shiftSchedule: 'shiftSchedule',
  generalAssignments: 'generalAssignments',
  notes: 'notes',
  profileImageUrl: 'profileImageUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.EmployeeStatusEntryScalarFieldEnum = {
  id: 'id',
  employeeId: 'employeeId',
  status: 'status',
  changedAt: 'changedAt',
  reason: 'reason'
};

exports.Prisma.ServiceRecordScalarFieldEnum = {
  id: 'id',
  vehicleId: 'vehicleId',
  employeeId: 'employeeId',
  date: 'date',
  odometer: 'odometer',
  servicePerformed: 'servicePerformed',
  notes: 'notes',
  cost: 'cost',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DelegationScalarFieldEnum = {
  id: 'id',
  eventName: 'eventName',
  location: 'location',
  durationFrom: 'durationFrom',
  durationTo: 'durationTo',
  invitationFrom: 'invitationFrom',
  invitationTo: 'invitationTo',
  flightArrivalId: 'flightArrivalId',
  flightDepartureId: 'flightDepartureId',
  status: 'status',
  notes: 'notes',
  imageUrl: 'imageUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DelegateScalarFieldEnum = {
  id: 'id',
  delegationId: 'delegationId',
  name: 'name',
  title: 'title',
  notes: 'notes'
};

exports.Prisma.DelegationStatusEntryScalarFieldEnum = {
  id: 'id',
  delegationId: 'delegationId',
  status: 'status',
  changedAt: 'changedAt',
  reason: 'reason'
};

exports.Prisma.FlightDetailsScalarFieldEnum = {
  id: 'id',
  flightNumber: 'flightNumber',
  dateTime: 'dateTime',
  airport: 'airport',
  terminal: 'terminal',
  notes: 'notes',
  arrivalDelegationId: 'arrivalDelegationId',
  departureDelegationId: 'departureDelegationId'
};

exports.Prisma.TaskScalarFieldEnum = {
  id: 'id',
  description: 'description',
  location: 'location',
  dateTime: 'dateTime',
  estimatedDuration: 'estimatedDuration',
  requiredSkills: 'requiredSkills',
  priority: 'priority',
  deadline: 'deadline',
  status: 'status',
  notes: 'notes',
  vehicleId: 'vehicleId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  assignedEmployeeIds: 'assignedEmployeeIds'
};

exports.Prisma.SubTaskScalarFieldEnum = {
  id: 'id',
  taskId: 'taskId',
  title: 'title',
  completed: 'completed'
};

exports.Prisma.TaskStatusEntryScalarFieldEnum = {
  id: 'id',
  taskId: 'taskId',
  status: 'status',
  changedAt: 'changedAt',
  reason: 'reason'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.UserRole = exports.$Enums.UserRole = {
  SUPER_ADMIN: 'SUPER_ADMIN',
  ADMIN: 'ADMIN',
  MANAGER: 'MANAGER',
  USER: 'USER',
  READONLY: 'READONLY'
};

exports.EmployeeRole = exports.$Enums.EmployeeRole = {
  driver: 'driver',
  mechanic: 'mechanic',
  administrator: 'administrator',
  office_staff: 'office_staff',
  manager: 'manager',
  service_advisor: 'service_advisor',
  technician: 'technician',
  other: 'other'
};

exports.EmployeeStatus = exports.$Enums.EmployeeStatus = {
  Active: 'Active',
  On_Leave: 'On_Leave',
  Terminated: 'Terminated',
  Inactive: 'Inactive'
};

exports.DriverAvailability = exports.$Enums.DriverAvailability = {
  On_Shift: 'On_Shift',
  Off_Shift: 'Off_Shift',
  On_Break: 'On_Break',
  Busy: 'Busy'
};

exports.DelegationStatus = exports.$Enums.DelegationStatus = {
  Planned: 'Planned',
  Confirmed: 'Confirmed',
  In_Progress: 'In_Progress',
  Completed: 'Completed',
  Cancelled: 'Cancelled',
  No_details: 'No_details'
};

exports.TaskPriority = exports.$Enums.TaskPriority = {
  Low: 'Low',
  Medium: 'Medium',
  High: 'High'
};

exports.TaskStatus = exports.$Enums.TaskStatus = {
  Pending: 'Pending',
  Assigned: 'Assigned',
  In_Progress: 'In_Progress',
  Completed: 'Completed',
  Cancelled: 'Cancelled'
};

exports.Prisma.ModelName = {
  UserProfile: 'UserProfile',
  Vehicle: 'Vehicle',
  Employee: 'Employee',
  EmployeeStatusEntry: 'EmployeeStatusEntry',
  ServiceRecord: 'ServiceRecord',
  Delegation: 'Delegation',
  Delegate: 'Delegate',
  DelegationStatusEntry: 'DelegationStatusEntry',
  FlightDetails: 'FlightDetails',
  Task: 'Task',
  SubTask: 'SubTask',
  TaskStatusEntry: 'TaskStatusEntry'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
