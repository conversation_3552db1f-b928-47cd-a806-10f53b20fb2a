# WorkHub Staging Environment Configuration
# Created: January 24, 2025
# Purpose: Staging deployment with 100% functional Hybrid RBAC system

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
NODE_ENV=staging
PORT=3001

# =============================================================================
# SUPABASE CONFIGURATION (Production Database)
# =============================================================================
SUPABASE_URL=https://abylqjnpaegeqwktcukn.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMTM0NTMsImV4cCI6MjA2Mjc4OTQ1M30.WCzj8fDu7vdxhvbOUuoQHVamy9-XYBr4vtTox52ap2o
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFieWxxam5wYWVnZXF3a3RjdWtuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzIxMzQ1MywiZXhwIjoyMDYyNzg5NDUzfQ.yLrGESZvVC6ISrqlcKeR3uvfRqdWPcZqYqLLZkjphU8

# Database URL for Prisma
DATABASE_URL=postgresql://postgres.abylqjnpaegeqwktcukn:<EMAIL>:5432/postgres

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Strong JWT Secret (32+ characters)
JWT_SECRET=JJxeCe52j/9tIaQLW9guDu+pBpSRp//c4PXnj7mV/oyTUdwSBGIOfmpKEAGPY3hA3cBkcu2o2xbw/FiHIKtFUw==

# API Secret for internal services
API_SECRET=GENERATE_32_CHAR_SECRET_FOR_STAGING

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Frontend URLs allowed for CORS (staging-specific)
FRONTEND_URL=http://localhost:3000,http://localhost:3001,https://your-staging-frontend.vercel.app

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_LEVEL=info

# =============================================================================
# FEATURE FLAGS
# =============================================================================
USE_SUPABASE=true

# =============================================================================
# STAGING-SPECIFIC CONFIGURATION
# =============================================================================
# Enable additional logging for staging
DEBUG_MODE=true

# Rate limiting (more permissive for testing)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=200

# =============================================================================
# DEPLOYMENT METADATA
# =============================================================================
DEPLOYMENT_ENV=staging
DEPLOYMENT_VERSION=1.0.0-rbac
DEPLOYMENT_DATE=2025-01-24
SECURITY_LEVEL=HIGH
