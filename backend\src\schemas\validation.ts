import {z} from 'zod';

/**
 * PHASE 1 SECURITY HARDENING: Input Validation Schemas
 * 
 * Comprehensive Zod schemas for validating and sanitizing all API inputs.
 * These schemas work with the inputValidation middleware to provide
 * robust protection against malicious input.
 */

// Common validation patterns
const emailSchema = z.string().email().max(254);
const phoneSchema = z.string().regex(/^[\+]?[1-9][\d]{0,15}$/).max(20);
const uuidSchema = z.string().uuid();
const dateSchema = z.string().datetime().or(z.date());
const positiveIntSchema = z.number().int().positive();
const nonEmptyStringSchema = z.string().min(1).max(1000);

// Employee validation schemas
export const employeeSchemas = {
	create: z.object({
		body: z.object({
			firstName: z.string().min(1).max(50),
			lastName: z.string().min(1).max(50),
			email: emailSchema,
			phone: phoneSchema.optional(),
			position: z.string().min(1).max(100),
			department: z.string().min(1).max(100).optional(),
			hireDate: dateSchema.optional(),
			isActive: z.boolean().default(true),
		}),
		params: z.object({}),
		query: z.object({}),
	}),

	update: z.object({
		body: z.object({
			firstName: z.string().min(1).max(50).optional(),
			lastName: z.string().min(1).max(50).optional(),
			email: emailSchema.optional(),
			phone: phoneSchema.optional(),
			position: z.string().min(1).max(100).optional(),
			department: z.string().min(1).max(100).optional(),
			hireDate: dateSchema.optional(),
			isActive: z.boolean().optional(),
		}),
		params: z.object({
			id: uuidSchema,
		}),
		query: z.object({}),
	}),

	getById: z.object({
		body: z.object({}),
		params: z.object({
			id: uuidSchema,
		}),
		query: z.object({}),
	}),

	list: z.object({
		body: z.object({}),
		params: z.object({}),
		query: z.object({
			page: z.string().regex(/^\d+$/).transform(Number).optional(),
			limit: z.string().regex(/^\d+$/).transform(Number).optional(),
			search: z.string().max(100).optional(),
			department: z.string().max(100).optional(),
			isActive: z.string().regex(/^(true|false)$/).transform(val => val === 'true').optional(),
		}),
	}),
};

// Vehicle validation schemas
export const vehicleSchemas = {
	create: z.object({
		body: z.object({
			make: z.string().min(1).max(50),
			model: z.string().min(1).max(50),
			year: z.number().int().min(1900).max(new Date().getFullYear() + 1),
			vin: z.string().length(17).regex(/^[A-HJ-NPR-Z0-9]{17}$/),
			licensePlate: z.string().min(1).max(20),
			color: z.string().min(1).max(30).optional(),
			mileage: z.number().int().min(0).optional(),
			fuelType: z.enum(['GASOLINE', 'DIESEL', 'ELECTRIC', 'HYBRID']).optional(),
			status: z.enum(['ACTIVE', 'MAINTENANCE', 'RETIRED']).default('ACTIVE'),
		}),
		params: z.object({}),
		query: z.object({}),
	}),

	update: z.object({
		body: z.object({
			make: z.string().min(1).max(50).optional(),
			model: z.string().min(1).max(50).optional(),
			year: z.number().int().min(1900).max(new Date().getFullYear() + 1).optional(),
			vin: z.string().length(17).regex(/^[A-HJ-NPR-Z0-9]{17}$/).optional(),
			licensePlate: z.string().min(1).max(20).optional(),
			color: z.string().min(1).max(30).optional(),
			mileage: z.number().int().min(0).optional(),
			fuelType: z.enum(['GASOLINE', 'DIESEL', 'ELECTRIC', 'HYBRID']).optional(),
			status: z.enum(['ACTIVE', 'MAINTENANCE', 'RETIRED']).optional(),
		}),
		params: z.object({
			id: uuidSchema,
		}),
		query: z.object({}),
	}),

	getById: z.object({
		body: z.object({}),
		params: z.object({
			id: uuidSchema,
		}),
		query: z.object({}),
	}),

	list: z.object({
		body: z.object({}),
		params: z.object({}),
		query: z.object({
			page: z.string().regex(/^\d+$/).transform(Number).optional(),
			limit: z.string().regex(/^\d+$/).transform(Number).optional(),
			search: z.string().max(100).optional(),
			status: z.enum(['ACTIVE', 'MAINTENANCE', 'RETIRED']).optional(),
			fuelType: z.enum(['GASOLINE', 'DIESEL', 'ELECTRIC', 'HYBRID']).optional(),
		}),
	}),
};

// Service Record validation schemas
export const serviceRecordSchemas = {
	create: z.object({
		body: z.object({
			vehicleId: uuidSchema,
			serviceType: z.string().min(1).max(100),
			description: z.string().min(1).max(5000),
			cost: z.number().min(0).max(999999.99),
			serviceDate: dateSchema,
			mileage: z.number().int().min(0).optional(),
			performedBy: z.string().min(1).max(100).optional(),
			notes: z.string().max(10000).optional(),
		}),
		params: z.object({}),
		query: z.object({}),
	}),

	update: z.object({
		body: z.object({
			vehicleId: uuidSchema.optional(),
			serviceType: z.string().min(1).max(100).optional(),
			description: z.string().min(1).max(5000).optional(),
			cost: z.number().min(0).max(999999.99).optional(),
			serviceDate: dateSchema.optional(),
			mileage: z.number().int().min(0).optional(),
			performedBy: z.string().min(1).max(100).optional(),
			notes: z.string().max(10000).optional(),
		}),
		params: z.object({
			id: uuidSchema,
		}),
		query: z.object({}),
	}),

	getById: z.object({
		body: z.object({}),
		params: z.object({
			id: uuidSchema,
		}),
		query: z.object({}),
	}),

	list: z.object({
		body: z.object({}),
		params: z.object({}),
		query: z.object({
			page: z.string().regex(/^\d+$/).transform(Number).optional(),
			limit: z.string().regex(/^\d+$/).transform(Number).optional(),
			vehicleId: uuidSchema.optional(),
			serviceType: z.string().max(100).optional(),
			startDate: dateSchema.optional(),
			endDate: dateSchema.optional(),
		}),
	}),
};

// Delegation validation schemas
export const delegationSchemas = {
	create: z.object({
		body: z.object({
			employeeId: uuidSchema,
			vehicleId: uuidSchema,
			purpose: z.string().min(1).max(200),
			destination: z.string().min(1).max(200),
			startDate: dateSchema,
			endDate: dateSchema,
			notes: z.string().max(2000).optional(),
			status: z.enum(['PENDING', 'APPROVED', 'ACTIVE', 'COMPLETED', 'CANCELLED']).default('PENDING'),
		}),
		params: z.object({}),
		query: z.object({}),
	}),

	update: z.object({
		body: z.object({
			employeeId: uuidSchema.optional(),
			vehicleId: uuidSchema.optional(),
			purpose: z.string().min(1).max(200).optional(),
			destination: z.string().min(1).max(200).optional(),
			startDate: dateSchema.optional(),
			endDate: dateSchema.optional(),
			notes: z.string().max(2000).optional(),
			status: z.enum(['PENDING', 'APPROVED', 'ACTIVE', 'COMPLETED', 'CANCELLED']).optional(),
		}),
		params: z.object({
			id: uuidSchema,
		}),
		query: z.object({}),
	}),

	getById: z.object({
		body: z.object({}),
		params: z.object({
			id: uuidSchema,
		}),
		query: z.object({}),
	}),

	list: z.object({
		body: z.object({}),
		params: z.object({}),
		query: z.object({
			page: z.string().regex(/^\d+$/).transform(Number).optional(),
			limit: z.string().regex(/^\d+$/).transform(Number).optional(),
			employeeId: uuidSchema.optional(),
			vehicleId: uuidSchema.optional(),
			status: z.enum(['PENDING', 'APPROVED', 'ACTIVE', 'COMPLETED', 'CANCELLED']).optional(),
			startDate: dateSchema.optional(),
			endDate: dateSchema.optional(),
		}),
	}),
};

// Task validation schemas
export const taskSchemas = {
	create: z.object({
		body: z.object({
			title: z.string().min(1).max(200),
			description: z.string().min(1).max(5000),
			assignedTo: uuidSchema.optional(),
			priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM'),
			status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).default('PENDING'),
			dueDate: dateSchema.optional(),
			delegationId: uuidSchema.optional(),
		}),
		params: z.object({}),
		query: z.object({}),
	}),

	update: z.object({
		body: z.object({
			title: z.string().min(1).max(200).optional(),
			description: z.string().min(1).max(5000).optional(),
			assignedTo: uuidSchema.optional(),
			priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
			status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).optional(),
			dueDate: dateSchema.optional(),
			delegationId: uuidSchema.optional(),
		}),
		params: z.object({
			id: uuidSchema,
		}),
		query: z.object({}),
	}),

	getById: z.object({
		body: z.object({}),
		params: z.object({
			id: uuidSchema,
		}),
		query: z.object({}),
	}),

	list: z.object({
		body: z.object({}),
		params: z.object({}),
		query: z.object({
			page: z.string().regex(/^\d+$/).transform(Number).optional(),
			limit: z.string().regex(/^\d+$/).transform(Number).optional(),
			assignedTo: uuidSchema.optional(),
			priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
			status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).optional(),
			delegationId: uuidSchema.optional(),
		}),
	}),
};

// Admin validation schemas
export const adminSchemas = {
	diagnostics: z.object({
		body: z.object({}),
		params: z.object({}),
		query: z.object({
			includeDetails: z.string().regex(/^(true|false)$/).transform(val => val === 'true').optional(),
		}),
	}),
};

export default {
	employeeSchemas,
	vehicleSchemas,
	serviceRecordSchemas,
	delegationSchemas,
	taskSchemas,
	adminSchemas,
};
