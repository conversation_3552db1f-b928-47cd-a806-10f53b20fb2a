import React, { useState } from 'react';
import { User, LogOut, Shield, Mail, Calendar, Loader2 } from 'lucide-react';
import { useAuthContext } from '../../contexts/AuthContext';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { 
	DropdownMenu, 
	DropdownMenuContent, 
	DropdownMenuItem, 
	DropdownMenuLabel, 
	DropdownMenuSeparator, 
	DropdownMenuTrigger 
} from '../ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';

interface UserProfileProps {
	variant?: 'dropdown' | 'card';
	showSignOut?: boolean;
}

/**
 * EMERGENCY SECURITY COMPONENT - User Profile
 * 
 * This component displays the current user's profile information
 * and provides access to account actions like sign out.
 * 
 * CRITICAL: This component is part of the emergency security implementation
 */
export function UserProfile({ variant = 'dropdown', showSignOut = true }: UserProfileProps) {
	const { user, signOut, loading } = useAuthContext();
	const [isSigningOut, setIsSigningOut] = useState(false);

	if (!user) {
		return null;
	}

	const handleSignOut = async () => {
		setIsSigningOut(true);
		try {
			await signOut();
		} catch (error) {
			console.error('Sign out error:', error);
		} finally {
			setIsSigningOut(false);
		}
	};

	const getUserInitials = (email: string) => {
		return email.substring(0, 2).toUpperCase();
	};

	const formatDate = (dateString: string | null) => {
		if (!dateString) return 'Never';
		return new Date(dateString).toLocaleDateString();
	};

	const getUserRole = () => {
		return user.user_metadata?.role || 'USER';
	};

	const getRoleBadgeVariant = (role: string) => {
		switch (role.toUpperCase()) {
			case 'ADMIN':
				return 'destructive';
			case 'MANAGER':
				return 'default';
			case 'USER':
			default:
				return 'secondary';
		}
	};

	if (variant === 'dropdown') {
		return (
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="ghost" className="relative h-8 w-8 rounded-full">
						<Avatar className="h-8 w-8">
							<AvatarImage src={user.user_metadata?.avatar_url} alt={user.email || ''} />
							<AvatarFallback>
								{getUserInitials(user.email || 'U')}
							</AvatarFallback>
						</Avatar>
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent className="w-56" align="end" forceMount>
					<DropdownMenuLabel className="font-normal">
						<div className="flex flex-col space-y-1">
							<p className="text-sm font-medium leading-none">
								{user.user_metadata?.full_name || user.email}
							</p>
							<p className="text-xs leading-none text-muted-foreground">
								{user.email}
							</p>
							<div className="flex items-center gap-2 mt-1">
								<Badge variant={getRoleBadgeVariant(getUserRole())} className="text-xs">
									{getUserRole()}
								</Badge>
								{user.email_confirmed_at && (
									<Badge variant="outline" className="text-xs">
										<Shield className="h-3 w-3 mr-1" />
										Verified
									</Badge>
								)}
							</div>
						</div>
					</DropdownMenuLabel>
					<DropdownMenuSeparator />
					{showSignOut && (
						<DropdownMenuItem 
							onClick={handleSignOut}
							disabled={isSigningOut}
							className="text-red-600 focus:text-red-600"
						>
							{isSigningOut ? (
								<>
									<Loader2 className="mr-2 h-4 w-4 animate-spin" />
									Signing out...
								</>
							) : (
								<>
									<LogOut className="mr-2 h-4 w-4" />
									Sign out
								</>
							)}
						</DropdownMenuItem>
					)}
				</DropdownMenuContent>
			</DropdownMenu>
		);
	}

	// Card variant
	return (
		<Card className="w-full max-w-md">
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<User className="h-5 w-5" />
					User Profile
				</CardTitle>
				<CardDescription>
					🚨 Emergency Security - Current session information
				</CardDescription>
			</CardHeader>
			<CardContent className="space-y-4">
				{/* User Avatar and Basic Info */}
				<div className="flex items-center space-x-4">
					<Avatar className="h-16 w-16">
						<AvatarImage src={user.user_metadata?.avatar_url} alt={user.email || ''} />
						<AvatarFallback className="text-lg">
							{getUserInitials(user.email || 'U')}
						</AvatarFallback>
					</Avatar>
					<div className="space-y-1">
						<h3 className="font-medium">
							{user.user_metadata?.full_name || 'User'}
						</h3>
						<div className="flex items-center gap-2">
							<Mail className="h-4 w-4 text-muted-foreground" />
							<span className="text-sm text-muted-foreground">{user.email}</span>
						</div>
						<div className="flex items-center gap-2">
							<Badge variant={getRoleBadgeVariant(getUserRole())}>
								{getUserRole()}
							</Badge>
							{user.email_confirmed_at && (
								<Badge variant="outline">
									<Shield className="h-3 w-3 mr-1" />
									Verified
								</Badge>
							)}
						</div>
					</div>
				</div>

				{/* Account Details */}
				<div className="space-y-2 text-sm">
					<div className="flex items-center justify-between">
						<span className="text-muted-foreground">User ID:</span>
						<span className="font-mono text-xs">{user.id.substring(0, 8)}...</span>
					</div>
					<div className="flex items-center justify-between">
						<span className="text-muted-foreground">Email Verified:</span>
						<span>{user.email_confirmed_at ? '✅ Yes' : '❌ No'}</span>
					</div>
					<div className="flex items-center justify-between">
						<span className="text-muted-foreground">Last Sign In:</span>
						<span>{formatDate(user.last_sign_in_at)}</span>
					</div>
					<div className="flex items-center justify-between">
						<span className="text-muted-foreground">Created:</span>
						<span>{formatDate(user.created_at)}</span>
					</div>
				</div>

				{/* Security Notice */}
				<Alert>
					<Shield className="h-4 w-4" />
					<AlertDescription className="text-xs">
						This session is protected by emergency security protocols. 
						All activities are monitored and logged.
					</AlertDescription>
				</Alert>

				{/* Sign Out Button */}
				{showSignOut && (
					<Button 
						onClick={handleSignOut}
						disabled={isSigningOut}
						variant="outline"
						className="w-full"
					>
						{isSigningOut ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Signing out...
							</>
						) : (
							<>
								<LogOut className="mr-2 h-4 w-4" />
								Sign out
							</>
						)}
					</Button>
				)}
			</CardContent>
		</Card>
	);
}
