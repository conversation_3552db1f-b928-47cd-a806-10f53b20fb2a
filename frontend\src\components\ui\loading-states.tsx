'use client';

import React from 'react';
import { Loader2, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';

/**
 * Props for LoadingSpinner component
 */
interface LoadingSpinnerProps {
  /** Size of the spinner in pixels */
  size?: number;
  /** Custom CSS class names */
  className?: string;
  /** Text to display next to the spinner */
  text?: string;
}

/**
 * A simple loading spinner component
 */
export function LoadingSpinner({ 
  size = 24, 
  className, 
  text 
}: LoadingSpinnerProps) {
  return (
    <div className={cn("flex items-center justify-center", className)}>
      <Loader2 
        className="animate-spin text-muted-foreground" 
        style={{ width: size, height: size }} 
      />
      {text && (
        <span className="ml-2 text-sm text-muted-foreground">{text}</span>
      )}
    </div>
  );
}

/**
 * Props for LoadingCard component
 */
interface LoadingCardProps {
  /** Number of skeleton items to display */
  count?: number;
  /** Height of each skeleton item */
  height?: number;
  /** Custom CSS class names */
  className?: string;
}

/**
 * A card loading state with skeleton UI
 */
export function LoadingCard({ 
  count = 3, 
  height = 20, 
  className 
}: LoadingCardProps) {
  return (
    <div className={cn("space-y-2", className)}>
      <Skeleton className="h-8 w-[250px] mb-4" />
      {Array.from({ length: count }).map((_, i) => (
        <Skeleton 
          key={i} 
          className="w-full" 
          style={{ height: `${height}px` }} 
        />
      ))}
    </div>
  );
}

/**
 * Props for LoadingError component
 */
interface LoadingErrorProps {
  /** Error message to display */
  message: string;
  /** Function to retry the operation */
  onRetry?: () => void;
  /** Custom CSS class names */
  className?: string;
}

/**
 * An error state component for failed loading operations
 */
export function LoadingError({ 
  message, 
  onRetry, 
  className 
}: LoadingErrorProps) {
  return (
    <Alert variant="destructive" className={cn("my-4", className)}>
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>
        <div className="mt-2">
          <p className="text-sm text-muted-foreground mb-4">{message}</p>
          {onRetry && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onRetry}
              className="flex items-center"
            >
              <Loader2 className="mr-2 h-4 w-4" />
              Retry
            </Button>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
}

/**
 * Props for LoadingContainer component
 */
interface LoadingContainerProps {
  /** Whether the data is currently loading */
  isLoading: boolean;
  /** Error message, if any */
  error?: string | null;
  /** Function to retry the operation */
  onRetry?: () => void;
  /** Content to display when loaded successfully */
  children: React.ReactNode;
  /** Custom loading component */
  loadingComponent?: React.ReactNode;
  /** Custom error component */
  errorComponent?: React.ReactNode;
  /** Custom CSS class names */
  className?: string;
}

/**
 * A container component that handles loading, error, and success states
 */
export function LoadingContainer({
  isLoading,
  error,
  onRetry,
  children,
  loadingComponent,
  errorComponent,
  className
}: LoadingContainerProps) {
  if (isLoading) {
    return loadingComponent || <LoadingSpinner className={className} text="Loading..." />;
  }
  
  if (error) {
    return errorComponent || <LoadingError message={error} onRetry={onRetry} className={className} />;
  }
  
  return <div className={className}>{children}</div>;
}
