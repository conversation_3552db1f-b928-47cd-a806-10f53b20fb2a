import {z} from 'zod';
import {Request, Response, NextFunction} from 'express';

/**
 * Middleware factory for validating request data against a Zod schema
 *
 * @param schema The Zod schema to validate against
 * @param source Where to find the data to validate (body, params, query)
 * @returns Express middleware function
 */
export const validate = (
	schema: z.ZodType,
	source: 'body' | 'params' | 'query' = 'body'
) => {
	return (req: Request, res: Response, next: NextFunction): void => {
		try {
			// Log the incoming data for debugging
			console.log(
				`Validating ${source} data:`,
				JSON.stringify(req[source], null, 2)
			);

			const result = schema.safeParse(req[source]);

			// Log the parsed/validated data
			if (source === 'query') {
				console.log(
					'Zod parsed query data (result.data):',
					JSON.stringify(result.success ? result.data : result.error, null, 2)
				);
			}

			if (!result.success) {
				// Format the validation errors for a cleaner response
				const formattedErrors = formatZodErrors(result.error, req[source]);

				// Log detailed validation errors
				console.error('Validation failed:', formattedErrors);
				console.error('Raw Zod error:', result.error);

				// Include the original data in the error response for debugging
				const sanitizedData = sanitizeData(req[source]);

				res.status(400).json({
					status: 'error',
					message: 'Validation failed',
					errors: formattedErrors,
					receivedData: sanitizedData,
					// Include more debug info for development
					...(process.env.NODE_ENV !== 'production' && {
						debugInfo: {
							zodErrors: result.error.issues,
							flattenedErrors: result.error.flatten(),
						},
					}),
				});
				return;
			}

			// Store the validated data into a new property on the request object
			// to avoid issues with modifying Express's built-in req.query, req.body, etc.
			(req as any).validatedData = result.data;

			next();
		} catch (error) {
			console.error('Unexpected error in validation middleware:', error);
			next(error);
		}
	};
};

/**
 * Helper function to format Zod errors into a more user-friendly structure
 */
const formatZodErrors = (error: z.ZodError, data?: any) => {
	return error.issues.map((issue) => ({
		path: issue.path.join('.'),
		message: issue.message,
		code: issue.code,
		received:
			issue.path.length > 0 && data
				? getValueAtPath(issue.path, data)
				: undefined,
	}));
};

/**
 * Helper to get the value at a specific path in an object
 */
const getValueAtPath = (path: (string | number)[], data: any) => {
	try {
		let value = path.reduce((obj, key) => obj[key], data);
		return typeof value === 'object' ? JSON.stringify(value) : value;
	} catch (e) {
		return undefined;
	}
};

/**
 * Sanitize data for logging/error responses by removing sensitive fields
 */
const sanitizeData = (data: any) => {
	if (!data || typeof data !== 'object') return data;

	// Create a deep copy to avoid modifying the original
	const sanitized = JSON.parse(JSON.stringify(data));

	// List of sensitive fields to redact
	const sensitiveFields = ['password', 'token', 'secret', 'apiKey', 'api_key'];

	// Recursive function to sanitize nested objects
	const sanitizeObject = (obj: any) => {
		if (!obj || typeof obj !== 'object') return;

		Object.keys(obj).forEach((key) => {
			// Check if this is a sensitive field
			if (
				sensitiveFields.some((field) =>
					key.toLowerCase().includes(field.toLowerCase())
				)
			) {
				obj[key] = '[REDACTED]';
			} else if (typeof obj[key] === 'object') {
				// Recursively sanitize nested objects and arrays
				sanitizeObject(obj[key]);
			}
		});
	};

	sanitizeObject(sanitized);
	return sanitized;
};
