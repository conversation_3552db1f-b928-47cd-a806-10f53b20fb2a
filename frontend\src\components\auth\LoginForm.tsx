import React, { useState } from 'react';
import { Eye, EyeOff, Lock, Mail, AlertCircle, Loader2 } from 'lucide-react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Alert, AlertDescription } from '../ui/alert';
import { useAuth } from '../../hooks/useAuth';

interface LoginFormProps {
	onSuccess?: () => void;
	onForgotPassword?: () => void;
	onSignUp?: () => void;
}

/**
 * EMERGENCY SECURITY COMPONENT - Login Form
 * 
 * This component provides a secure login interface using Supabase authentication.
 * 
 * CRITICAL: This component is part of the emergency security implementation
 */
export function LoginForm({ onSuccess, onForgotPassword, onSignUp }: LoginFormProps) {
	const { signIn, loading, error, clearError } = useAuth();
	const [formData, setFormData] = useState({
		email: '',
		password: '',
	});
	const [showPassword, setShowPassword] = useState(false);
	const [formErrors, setFormErrors] = useState<Record<string, string>>({});

	// Validate form data
	const validateForm = () => {
		const errors: Record<string, string> = {};

		if (!formData.email) {
			errors.email = 'Email is required';
		} else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
			errors.email = 'Please enter a valid email address';
		}

		if (!formData.password) {
			errors.password = 'Password is required';
		} else if (formData.password.length < 6) {
			errors.password = 'Password must be at least 6 characters';
		}

		setFormErrors(errors);
		return Object.keys(errors).length === 0;
	};

	// Handle form submission
	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		
		// Clear previous errors
		clearError();
		setFormErrors({});

		// Validate form
		if (!validateForm()) {
			return;
		}

		try {
			const { error } = await signIn(formData.email, formData.password);
			
			if (!error) {
				// Success - call onSuccess callback if provided
				onSuccess?.();
			}
			// Error handling is managed by the useAuth hook
		} catch (err) {
			console.error('Login error:', err);
		}
	};

	// Handle input changes
	const handleInputChange = (field: string, value: string) => {
		setFormData(prev => ({ ...prev, [field]: value }));
		
		// Clear field error when user starts typing
		if (formErrors[field]) {
			setFormErrors(prev => ({ ...prev, [field]: '' }));
		}
		
		// Clear global error when user starts typing
		if (error) {
			clearError();
		}
	};

	return (
		<Card className="w-full max-w-md mx-auto">
			<CardHeader className="space-y-1">
				<CardTitle className="text-2xl font-bold text-center">
					🚨 WorkHub Login
				</CardTitle>
				<CardDescription className="text-center">
					Emergency Security Access - Enter your credentials to continue
				</CardDescription>
			</CardHeader>
			<CardContent>
				<form onSubmit={handleSubmit} className="space-y-4">
					{/* Global Error Alert */}
					{error && (
						<Alert variant="destructive">
							<AlertCircle className="h-4 w-4" />
							<AlertDescription>{error}</AlertDescription>
						</Alert>
					)}

					{/* Email Field */}
					<div className="space-y-2">
						<Label htmlFor="email">Email Address</Label>
						<div className="relative">
							<Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
							<Input
								id="email"
								type="email"
								placeholder="Enter your email"
								value={formData.email}
								onChange={(e) => handleInputChange('email', e.target.value)}
								className={`pl-10 ${formErrors.email ? 'border-red-500' : ''}`}
								disabled={loading}
								autoComplete="email"
							/>
						</div>
						{formErrors.email && (
							<p className="text-sm text-red-500">{formErrors.email}</p>
						)}
					</div>

					{/* Password Field */}
					<div className="space-y-2">
						<Label htmlFor="password">Password</Label>
						<div className="relative">
							<Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
							<Input
								id="password"
								type={showPassword ? 'text' : 'password'}
								placeholder="Enter your password"
								value={formData.password}
								onChange={(e) => handleInputChange('password', e.target.value)}
								className={`pl-10 pr-10 ${formErrors.password ? 'border-red-500' : ''}`}
								disabled={loading}
								autoComplete="current-password"
							/>
							<button
								type="button"
								onClick={() => setShowPassword(!showPassword)}
								className="absolute right-3 top-3 h-4 w-4 text-muted-foreground hover:text-foreground"
								disabled={loading}
							>
								{showPassword ? <EyeOff /> : <Eye />}
							</button>
						</div>
						{formErrors.password && (
							<p className="text-sm text-red-500">{formErrors.password}</p>
						)}
					</div>

					{/* Submit Button */}
					<Button 
						type="submit" 
						className="w-full" 
						disabled={loading}
					>
						{loading ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Signing In...
							</>
						) : (
							'Sign In'
						)}
					</Button>

					{/* Action Links */}
					<div className="space-y-2 text-center">
						{onForgotPassword && (
							<button
								type="button"
								onClick={onForgotPassword}
								className="text-sm text-muted-foreground hover:text-foreground underline"
								disabled={loading}
							>
								Forgot your password?
							</button>
						)}
						
						{onSignUp && (
							<div className="text-sm text-muted-foreground">
								Don't have an account?{' '}
								<button
									type="button"
									onClick={onSignUp}
									className="text-foreground hover:underline font-medium"
									disabled={loading}
								>
									Sign up here
								</button>
							</div>
						)}
					</div>
				</form>

				{/* Emergency Security Notice */}
				<div className="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
					<div className="flex items-start">
						<AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
						<div className="text-xs text-yellow-800">
							<strong>Emergency Security Mode:</strong> This system is operating under enhanced security protocols. 
							All access attempts are logged and monitored.
						</div>
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
