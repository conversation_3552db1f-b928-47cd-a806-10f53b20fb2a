import {Server as SocketServer} from 'socket.io';
import {supabase} from './database.service.js';
import logger from '../utils/logger.js';

// Socket.io event names - must match frontend
export const SOCKET_EVENTS = {
	VEHICLE_UPDATED: 'vehicle:updated',
	VEHICLE_CREATED: 'vehicle:created',
	VEHICLE_DELETED: 'vehicle:deleted',
	EMPLOYEE_UPDATED: 'employee:updated',
	EMPLOYEE_CREATED: 'employee:created',
	EMPLOYEE_DELETED: 'employee:deleted',
	REFRESH_VEHICLES: 'refresh:vehicles',
	REFRESH_EMPLOYEES: 'refresh:employees',
};

/**
 * Service to bridge Supabase real-time events to Socket.io
 */
class SupabaseRealtimeService {
	private io: SocketServer | null = null;
	private subscriptions: any[] = [];
	private isInitialized = false;

	/**
	 * Initialize the service with a Socket.io server instance
	 */
	initialize(io: SocketServer) {
		if (this.isInitialized) {
			logger.warn('SupabaseRealtimeService already initialized');
			return;
		}

		this.io = io;
		this.isInitialized = true;
		logger.info('SupabaseRealtimeService initialized');

		// Only set up subscriptions if Supabase client is available
		if (supabase) {
			this.setupSubscriptions();
		} else {
			logger.warn(
				'Supabase client not available, real-time subscriptions not set up'
			);
		}
	}

	/**
	 * Set up Supabase real-time subscriptions
	 */
	private setupSubscriptions() {
		if (!supabase || !this.io) {
			logger.error(
				'Cannot set up subscriptions: Supabase or Socket.io not initialized'
			);
			return;
		}

		try {
			// Subscribe to vehicle changes
			const vehiclesChannel = supabase
				.channel('vehicles-changes')
				.on(
					'postgres_changes',
					{event: 'INSERT', schema: 'public', table: 'vehicles'},
					(payload) => {
						logger.info('Vehicle created:', payload.new.id);
						this.io?.emit(SOCKET_EVENTS.VEHICLE_CREATED, payload.new);
						this.io?.emit(SOCKET_EVENTS.REFRESH_VEHICLES);
					}
				)
				.on(
					'postgres_changes',
					{event: 'UPDATE', schema: 'public', table: 'vehicles'},
					(payload) => {
						logger.info('Vehicle updated:', payload.new.id);
						this.io?.emit(SOCKET_EVENTS.VEHICLE_UPDATED, payload.new);
						this.io?.emit(SOCKET_EVENTS.REFRESH_VEHICLES);
					}
				)
				.on(
					'postgres_changes',
					{event: 'DELETE', schema: 'public', table: 'vehicles'},
					(payload) => {
						logger.info('Vehicle deleted:', payload.old.id);
						this.io?.emit(SOCKET_EVENTS.VEHICLE_DELETED, payload.old);
						this.io?.emit(SOCKET_EVENTS.REFRESH_VEHICLES);
					}
				)
				.subscribe();

			// Subscribe to employee changes
			const employeesChannel = supabase
				.channel('employees-changes')
				.on(
					'postgres_changes',
					{event: 'INSERT', schema: 'public', table: 'employees'},
					(payload) => {
						logger.info('Employee created:', payload.new.id);
						this.io?.emit(SOCKET_EVENTS.EMPLOYEE_CREATED, payload.new);
						this.io?.emit(SOCKET_EVENTS.REFRESH_EMPLOYEES);
					}
				)
				.on(
					'postgres_changes',
					{event: 'UPDATE', schema: 'public', table: 'employees'},
					(payload) => {
						logger.info('Employee updated:', payload.new.id);
						this.io?.emit(SOCKET_EVENTS.EMPLOYEE_UPDATED, payload.new);
						this.io?.emit(SOCKET_EVENTS.REFRESH_EMPLOYEES);
					}
				)
				.on(
					'postgres_changes',
					{event: 'DELETE', schema: 'public', table: 'employees'},
					(payload) => {
						logger.info('Employee deleted:', payload.old.id);
						this.io?.emit(SOCKET_EVENTS.EMPLOYEE_DELETED, payload.old);
						this.io?.emit(SOCKET_EVENTS.REFRESH_EMPLOYEES);
					}
				)
				.subscribe();

			// Store subscriptions for cleanup
			this.subscriptions.push(vehiclesChannel, employeesChannel);

			logger.info('Supabase real-time subscriptions set up successfully');
		} catch (error) {
			logger.error('Error setting up Supabase real-time subscriptions:', error);
		}
	}

	/**
	 * Clean up subscriptions when shutting down
	 */
	cleanup() {
		if (supabase) {
			this.subscriptions.forEach((channel) => {
				// Add null check before calling removeChannel
				supabase?.removeChannel(channel);
			});
			this.subscriptions = [];
		}

		this.isInitialized = false;
		logger.info('SupabaseRealtimeService cleaned up');
	}
}

// Export singleton instance
export const supabaseRealtimeService = new SupabaseRealtimeService();
export default supabaseRealtimeService;
