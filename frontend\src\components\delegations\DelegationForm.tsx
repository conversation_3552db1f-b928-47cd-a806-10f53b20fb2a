// @ts-nocheck
'use client';

import {useForm, useFieldArray, type SubmitHandler} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {
	DelegationFormSchema,
	type DelegationFormData,
	DelegationStatusSchema,
} from '@/lib/schemas/delegationSchemas';
import type {Delegation, DelegationStatus} from '@/lib/types';
import {ActionButton} from '@/components/ui/action-button';
import {Input} from '@/components/ui/input';
import {Textarea} from '@/components/ui/textarea';
import {Label} from '@/components/ui/label';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardDescription,
	CardFooter,
} from '@/components/ui/card';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {Separator} from '@/components/ui/separator';
import {
	PlusCircle,
	Trash2,
	Save,
	ArrowLeft,
	PlaneTakeoff,
	PlaneLanding,
	Users,
	Info,
	Search,
	Loader2,
} from 'lucide-react';
import {useRouter} from 'next/navigation';
import {format, parseISO} from 'date-fns';
import {useState} from 'react';
import {useToast} from '@/hooks/use-toast';
import FlightSearchModal from './FlightSearchModal';
import {formatDelegationStatusForDisplay} from '@/lib/utils/formattingUtils';

interface DelegationFormProps {
	onSubmit: (data: DelegationFormData) => void;
	initialData?: Delegation;
	isEditing?: boolean;
	isSubmitting?: boolean;
}

// Helper to format date for input type="date" and type="datetime-local"
const formatDateForInput = (
	isoDateString: string | undefined,
	type: 'date' | 'datetime-local'
): string => {
	if (!isoDateString) return '';
	try {
		const date = parseISO(isoDateString);
		if (type === 'date') {
			return format(date, 'yyyy-MM-dd');
		}
		// For datetime-local, format is 'yyyy-MM-ddTHH:mm'
		return format(date, "yyyy-MM-dd'T'HH:mm");
	} catch (error) {
		console.warn('Invalid date string for input formatting:', isoDateString);
		return '';
	}
};

export default function DelegationForm({
	onSubmit,
	initialData,
	isEditing = false,
	isSubmitting = false,
}: DelegationFormProps) {
	const router = useRouter();
	const {toast} = useToast();

	// State for flight search modals
	const [isArrivalModalOpen, setIsArrivalModalOpen] = useState(false);
	const [isDepartureModalOpen, setIsDepartureModalOpen] = useState(false);

	const form = useForm<DelegationFormData>({
		resolver: zodResolver(DelegationFormSchema),
		defaultValues: {
			eventName: initialData?.eventName || '',
			location: initialData?.location || '',
			durationFrom: initialData
				? formatDateForInput(initialData.durationFrom, 'date')
				: '',
			durationTo: initialData
				? formatDateForInput(initialData.durationTo, 'date')
				: '',
			invitationFrom: initialData?.invitationFrom || '',
			invitationTo: initialData?.invitationTo || '',
			delegates: initialData?.delegates?.length
				? initialData.delegates
				: [{name: '', title: '', notes: ''}],
			flightArrivalDetails: initialData?.flightArrivalDetails
				? {
						...initialData.flightArrivalDetails,
						dateTime: formatDateForInput(
							initialData.flightArrivalDetails.dateTime,
							'datetime-local'
						),
				  }
				: null,
			flightDepartureDetails: initialData?.flightDepartureDetails
				? {
						...initialData.flightDepartureDetails,
						dateTime: formatDateForInput(
							initialData.flightDepartureDetails.dateTime,
							'datetime-local'
						),
				  }
				: null,
			status: initialData?.status || 'Planned',
			notes: initialData?.notes || '',
			imageUrl: initialData?.imageUrl || '',
		},
	});

	const {
		fields: delegateFields,
		append: appendDelegate,
		remove: removeDelegate,
	} = useFieldArray({
		control: form.control,
		name: 'delegates',
	});

	const handleFormSubmit: SubmitHandler<DelegationFormData> = (data) => {
		const processedData = {
			...data,
			flightArrivalDetails: processFlightDetails(data.flightArrivalDetails),
			flightDepartureDetails: processFlightDetails(data.flightDepartureDetails),
		};
		onSubmit(processedData);
	};

	const processFlightDetails = (details: any) => {
		if (!details) return null;
		const isEmpty =
			(!details.flightNumber || details.flightNumber.trim() === '') &&
			(!details.dateTime || details.dateTime.trim() === '') &&
			(!details.airport || details.airport.trim() === '');
		if (isEmpty) return null;
		const isComplete =
			details.flightNumber &&
			details.flightNumber.trim() !== '' &&
			details.dateTime &&
			details.dateTime.trim() !== '' &&
			details.airport &&
			details.airport.trim() !== '';
		if (!isComplete) {
			console.warn('Incomplete flight details detected:', details);
			return null;
		}
		try {
			const {
				formatDateForApi,
				isValidDateString,
			} = require('@/lib/utils/dateUtils');
			const processedDetails = {...details};
			if (processedDetails.dateTime) {
				processedDetails.dateTime = formatDateForApi(processedDetails.dateTime);
				if (!processedDetails.dateTime) {
					console.error(
						'Invalid date format after processing:',
						details.dateTime
					);
					return null;
				}
			}
			return processedDetails;
		} catch (error) {
			console.error('Error processing flight details:', error);
			return null;
		}
	};

	const handleFlightSelection = (
		flight: any,
		type: 'arrival' | 'departure'
	) => {
		try {
			const timestamp =
				type === 'arrival' ? flight.arrivalTime : flight.departureTime;
			let dateTimeValue = '';
			if (timestamp) {
				const date = new Date(timestamp * 1000);
				dateTimeValue = format(date, "yyyy-MM-dd'T'HH:mm");
			} else {
				const defaultDate = new Date();
				if (type === 'departure' && form.getValues('durationFrom')) {
					try {
						const durationFrom = parseISO(form.getValues('durationFrom'));
						defaultDate.setDate(durationFrom.getDate() - 1);
					} catch (error) {
						console.warn(
							'Could not parse durationFrom date, using current date'
						);
					}
				}
				dateTimeValue = format(defaultDate, "yyyy-MM-dd'T'HH:mm");
			}

			if (type === 'arrival') {
				form.setValue('flightArrivalDetails.flightNumber', flight.callsign);
				form.setValue('flightArrivalDetails.dateTime', dateTimeValue);
				form.setValue(
					'flightArrivalDetails.airport',
					flight.arrivalAirport || ''
				);
			} else {
				form.setValue('flightDepartureDetails.flightNumber', flight.callsign);
				form.setValue('flightDepartureDetails.dateTime', dateTimeValue);
				form.setValue(
					'flightDepartureDetails.airport',
					flight.departureAirport || ''
				);
			}
			toast({
				title: 'Flight Details Updated',
				description: `${
					type === 'arrival' ? 'Arrival' : 'Departure'
				} flight details have been populated.`,
				variant: 'default',
			});
		} catch (error) {
			console.error('Error setting flight details:', error);
			toast({
				title: 'Error',
				description:
					'Failed to set flight details. Please try again or enter manually.',
				variant: 'destructive',
			});
		}
	};

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(handleFormSubmit)}>
				<Card className='shadow-lg'>
					<CardHeader>
						<CardTitle className='text-2xl text-primary'>
							{isEditing ? 'Edit Delegation' : 'Add New Delegation'}
						</CardTitle>
						<CardDescription>
							Fill in the details for the delegation event or trip.
						</CardDescription>
					</CardHeader>
					<CardContent className='space-y-6'>
						<section className='space-y-4 p-6 border rounded-lg bg-card'>
							<h3 className='text-lg font-semibold text-foreground flex items-center'>
								<Info className='mr-2 h-5 w-5 text-accent' />
								Basic Information
							</h3>
							<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
								<FormField
									control={form.control}
									name='eventName'
									render={({field}) => (
										<FormItem>
											<FormLabel>Event Name</FormLabel>
											<FormControl>
												<Input
													placeholder='e.g., Annual Summit 2024'
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name='location'
									render={({field}) => (
										<FormItem>
											<FormLabel>Location</FormLabel>
											<FormControl>
												<Input placeholder='e.g., New York, USA' {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
							<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
								<FormField
									control={form.control}
									name='durationFrom'
									render={({field}) => (
										<FormItem>
											<FormLabel>Duration From</FormLabel>
											<FormControl>
												<Input type='date' {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name='durationTo'
									render={({field}) => (
										<FormItem>
											<FormLabel>Duration To</FormLabel>
											<FormControl>
												<Input type='date' {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
							<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
								<FormField
									control={form.control}
									name='invitationFrom'
									render={({field}) => (
										<FormItem>
											<FormLabel>Invitation From (Optional)</FormLabel>
											<FormControl>
												<Input placeholder='e.g., Global Corp' {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name='invitationTo'
									render={({field}) => (
										<FormItem>
											<FormLabel>Invitation To (Optional)</FormLabel>
											<FormControl>
												<Input placeholder='e.g., Our CEO' {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
							<FormField
								control={form.control}
								name='status'
								render={({field}) => (
									<FormItem>
										<FormLabel>Status</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder='Select status' />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{DelegationStatusSchema.options.map((statusVal) => (
													<SelectItem key={statusVal} value={statusVal}>
														{formatDelegationStatusForDisplay(
															statusVal as DelegationStatus
														)}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name='imageUrl'
								render={({field}) => (
									<FormItem>
										<FormLabel>Image URL (Optional)</FormLabel>
										<FormControl>
											<Input
												placeholder='https://example.com/image.jpg'
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</section>

						<section className='space-y-4 p-6 border rounded-lg bg-card'>
							<h3 className='text-lg font-semibold text-foreground flex items-center'>
								<Users className='mr-2 h-5 w-5 text-accent' />
								Delegates
							</h3>
							{delegateFields.map((field, index) => (
								<div
									key={field.id}
									className='p-3 border rounded-md space-y-3 bg-background relative'>
									<FormField
										control={form.control}
										name={`delegates.${index}.name`}
										render={({field: delegateField}) => (
											<FormItem>
												<FormLabel>Delegate Name</FormLabel>
												<FormControl>
													<Input placeholder='Full Name' {...delegateField} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`delegates.${index}.title`}
										render={({field: delegateField}) => (
											<FormItem>
												<FormLabel>Title/Role</FormLabel>
												<FormControl>
													<Input
														placeholder='e.g., CEO, Head of Department'
														{...delegateField}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`delegates.${index}.notes`}
										render={({field: delegateField}) => (
											<FormItem>
												<FormLabel>Notes (Optional)</FormLabel>
												<FormControl>
													<Textarea
														placeholder='Any specific notes for this delegate'
														{...delegateField}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									{delegateFields.length > 1 && (
										<ActionButton
											type='button'
											actionType='danger'
											size='sm'
											onClick={() => removeDelegate(index)}
											className='absolute top-2 right-2'
											icon={<Trash2 className='h-4 w-4' />}></ActionButton>
									)}
								</div>
							))}
							<ActionButton
								type='button'
								actionType='secondary'
								onClick={() => appendDelegate({name: '', title: '', notes: ''})}
								icon={<PlusCircle className='h-4 w-4' />}>
								Add Delegate
							</ActionButton>
						</section>

						<div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
							<section className='space-y-4 p-6 border rounded-lg bg-card'>
								<div className='flex justify-between items-center'>
									<h3 className='text-lg font-semibold text-foreground flex items-center'>
										<PlaneLanding className='mr-2 h-5 w-5 text-accent' />
										Arrival Flight (Optional)
									</h3>
									<ActionButton
										type='button'
										actionType='tertiary'
										size='sm'
										onClick={() => setIsArrivalModalOpen(true)}
										icon={<Search className='h-4 w-4' />}>
										Search Flights
									</ActionButton>
								</div>
								<FormField
									control={form.control}
									name='flightArrivalDetails.flightNumber'
									render={({field}) => (
										<FormItem>
											<FormLabel>Flight Number</FormLabel>
											<FormControl>
												<Input
													placeholder='e.g., BA245'
													{...field}
													value={field.value || ''}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name='flightArrivalDetails.dateTime'
									render={({field}) => (
										<FormItem>
											<FormLabel>Date & Time</FormLabel>
											<FormControl>
												<Input
													type='datetime-local'
													{...field}
													value={field.value || ''}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name='flightArrivalDetails.airport'
									render={({field}) => (
										<FormItem>
											<FormLabel>Airport</FormLabel>
											<FormControl>
												<Input
													placeholder='e.g., JFK'
													{...field}
													value={field.value || ''}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name='flightArrivalDetails.terminal'
									render={({field}) => (
										<FormItem>
											<FormLabel>Terminal (Optional)</FormLabel>
											<FormControl>
												<Input
													placeholder='e.g., T4'
													{...field}
													value={field.value || ''}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name='flightArrivalDetails.notes'
									render={({field}) => (
										<FormItem>
											<FormLabel>Notes (Optional)</FormLabel>
											<FormControl>
												<Textarea
													placeholder='Arrival notes'
													{...field}
													value={field.value || ''}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</section>

							<section className='space-y-4 p-6 border rounded-lg bg-card'>
								<div className='flex justify-between items-center'>
									<h3 className='text-lg font-semibold text-foreground flex items-center'>
										<PlaneTakeoff className='mr-2 h-5 w-5 text-accent' />
										Departure Flight (Optional)
									</h3>
									<ActionButton
										type='button'
										actionType='tertiary'
										size='sm'
										onClick={() => setIsDepartureModalOpen(true)}
										icon={<Search className='h-4 w-4' />}>
										Search Flights
									</ActionButton>
								</div>
								<FormField
									control={form.control}
									name='flightDepartureDetails.flightNumber'
									render={({field}) => (
										<FormItem>
											<FormLabel>Flight Number</FormLabel>
											<FormControl>
												<Input
													placeholder='e.g., AF123'
													{...field}
													value={field.value || ''}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name='flightDepartureDetails.dateTime'
									render={({field}) => (
										<FormItem>
											<FormLabel>Date & Time</FormLabel>
											<FormControl>
												<Input
													type='datetime-local'
													{...field}
													value={field.value || ''}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name='flightDepartureDetails.airport'
									render={({field}) => (
										<FormItem>
											<FormLabel>Airport</FormLabel>
											<FormControl>
												<Input
													placeholder='e.g., LHR'
													{...field}
													value={field.value || ''}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name='flightDepartureDetails.terminal'
									render={({field}) => (
										<FormItem>
											<FormLabel>Terminal (Optional)</FormLabel>
											<FormControl>
												<Input
													placeholder='e.g., T5'
													{...field}
													value={field.value || ''}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name='flightDepartureDetails.notes'
									render={({field}) => (
										<FormItem>
											<FormLabel>Notes (Optional)</FormLabel>
											<FormControl>
												<Textarea
													placeholder='Departure notes'
													{...field}
													value={field.value || ''}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</section>
						</div>

						<FlightSearchModal
							isOpen={isArrivalModalOpen}
							onClose={() => setIsArrivalModalOpen(false)}
							onSelectFlight={(flight) =>
								handleFlightSelection(flight, 'arrival')
							}
							type='arrival'
						/>

						<FlightSearchModal
							isOpen={isDepartureModalOpen}
							onClose={() => setIsDepartureModalOpen(false)}
							onSelectFlight={(flight) =>
								handleFlightSelection(flight, 'departure')
							}
							type='departure'
						/>

						<section className='space-y-4 p-6 border rounded-lg bg-card'>
							<h3 className='text-lg font-semibold text-foreground flex items-center'>
								<Info className='mr-2 h-5 w-5 text-accent' />
								General Notes (Optional)
							</h3>
							<FormField
								control={form.control}
								name='notes'
								render={({field}) => (
									<FormItem>
										<FormLabel>Additional Notes for the Delegation</FormLabel>
										<FormControl>
											<Textarea
												placeholder='Any other relevant information...'
												{...field}
												value={field.value || ''}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</section>
					</CardContent>
					<CardFooter className='flex justify-between gap-2 border-t pt-6'>
						<ActionButton
							type='button'
							actionType='tertiary'
							onClick={() => router.back()}
							icon={<ArrowLeft className='h-4 w-4' />}
							disabled={isSubmitting}>
							Cancel
						</ActionButton>
						<ActionButton
							type='submit'
							actionType='primary'
							isLoading={isSubmitting}
							loadingText={isEditing ? 'Saving...' : 'Creating...'}
							icon={<Save className='h-4 w-4' />}>
							{isEditing ? 'Save Changes' : 'Create Delegation'}
						</ActionButton>
					</CardFooter>
				</Card>
			</form>
		</Form>
	);
}
