'use client';

import React, { ReactNode } from 'react';
import ErrorBoundary from './ErrorBoundary';

interface DelegationErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * Specialized error boundary for delegation components
 * Uses the generic ErrorBoundary with delegation specific messaging
 */
const DelegationErrorBoundary: React.FC<DelegationErrorBoundaryProps> = ({ 
  children, 
  fallback 
}) => {
  return (
    <ErrorBoundary
      title="Error Loading Delegation"
      description="An unexpected error occurred while loading or updating delegation data."
      resetLabel="Try Again"
      fallback={fallback}
      onError={(error, errorInfo) => {
        // Log the error with delegation context
        console.error('Delegation component error:', error);
        console.error('Component stack:', errorInfo.componentStack);
        
        // In a production app, you would send this to a monitoring service
        // Example: errorReportingService.captureError(error, {
        //   context: 'Delegation',
        //   componentStack: errorInfo.componentStack
        // });
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

export default DelegationErrorBoundary;
