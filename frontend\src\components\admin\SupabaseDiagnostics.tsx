'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ConnectionStatus } from './ConnectionStatus';
import { HealthMetrics } from './HealthMetrics';
import { ErrorLog } from './ErrorLog';
import { PerformanceStats } from './PerformanceStats';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Database, Activity, AlertTriangle, Gauge } from 'lucide-react';

export function SupabaseDiagnostics() {
  return (
    <Card className="border-none shadow-none">
      <CardHeader className="px-0 pt-0">
        <CardTitle className="text-2xl font-bold">Supabase Diagnostics</CardTitle>
        <CardDescription>
          Monitor and troubleshoot your Supabase database connection
        </CardDescription>
      </CardHeader>
      <CardContent className="px-0">
        <Tabs defaultValue="connection" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="connection" className="flex items-center">
              <Database className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Connection</span>
            </TabsTrigger>
            <TabsTrigger value="health" className="flex items-center">
              <Gauge className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Health</span>
            </TabsTrigger>
            <TabsTrigger value="errors" className="flex items-center">
              <AlertTriangle className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Errors</span>
            </TabsTrigger>
            <TabsTrigger value="performance" className="flex items-center">
              <Activity className="mr-2 h-4 w-4" />
              <span className="hidden sm:inline">Performance</span>
            </TabsTrigger>
          </TabsList>
          <TabsContent value="connection" className="mt-4">
            <ConnectionStatus />
          </TabsContent>
          <TabsContent value="health" className="mt-4">
            <HealthMetrics />
          </TabsContent>
          <TabsContent value="errors" className="mt-4">
            <ErrorLog />
          </TabsContent>
          <TabsContent value="performance" className="mt-4">
            <PerformanceStats />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
