import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import {PerformanceStats} from '../PerformanceStats';
import {getMockPerformanceMetrics} from '@/lib/adminService';
import {useApi} from '@/hooks/useApi';

// Mock the hooks
jest.mock('@/hooks/useApi', () => ({
	useApi: jest.fn(),
}));

// Mock the components
jest.mock('@/components/ui/loading-states', () => ({
	LoadingError: ({
		message,
		onRetry,
	}: {
		message: string;
		onRetry: () => void;
	}) => (
		<div data-testid='loading-error'>
			<p>{message}</p>
			<button onClick={onRetry}>Retry</button>
		</div>
	),
}));

jest.mock('@/components/ui/error-boundary', () => ({
	ErrorBoundary: ({children}: {children: React.ReactNode}) => (
		<div>{children}</div>
	),
}));

// Mock the chart components
jest.mock('@/components/ui/chart', () => ({
	Chart: ({children}: {children: React.ReactNode}) => (
		<div data-testid='chart'>{children}</div>
	),
	ChartContainer: ({
		children,
		config,
	}: {
		children: React.ReactNode;
		config: any;
	}) => <div data-testid='chart-container'>{children}</div>,
	ChartTooltip: ({children}: {children: React.ReactNode}) => (
		<div>{children}</div>
	),
	ChartLegend: ({children}: {children: React.ReactNode}) => (
		<div>{children}</div>
	),
	ChartLegendContent: () => <div data-testid='chart-legend'>Legend</div>,
	ChartLine: ({dataKey}: {dataKey: string}) => (
		<div data-testid={`chart-line-${dataKey}`}>{dataKey}</div>
	),
	ChartLineArea: () => <div>LineArea</div>,
	ChartGrid: () => <div data-testid='chart-grid'>Grid</div>,
	ChartXAxis: () => <div data-testid='chart-x-axis'>X Axis</div>,
	ChartYAxis: () => <div data-testid='chart-y-axis'>Y Axis</div>,
	ChartTooltipContent: () => <div data-testid='chart-tooltip'>Tooltip</div>,
	ChartTooltipItem: () => <div>TooltipItem</div>,
}));

describe('PerformanceStats Component', () => {
	const mockRefetch = jest.fn();

	beforeEach(() => {
		jest.clearAllMocks();
	});

	it('should render loading state correctly', () => {
		(useApi as jest.Mock).mockReturnValue({
			data: null,
			isLoading: true,
			error: null,
			refetch: mockRefetch,
		});

		render(<PerformanceStats />);

		// Check for Skeleton component without testId
		expect(
			screen.getByText(/loading/i, {exact: false}) ||
				document.querySelector('.h-\\[200px\\]')
		).toBeTruthy();
	});

	it('should render error state correctly', () => {
		(useApi as jest.Mock).mockReturnValue({
			data: null,
			isLoading: false,
			error: 'Failed to fetch performance statistics',
			refetch: mockRefetch,
		});

		render(<PerformanceStats />);

		expect(screen.getByTestId('loading-error')).toBeInTheDocument();
		expect(
			screen.getByText('Failed to fetch performance statistics')
		).toBeInTheDocument();

		// Test retry functionality
		fireEvent.click(screen.getByText('Retry'));
		expect(mockRefetch).toHaveBeenCalledTimes(1);
	});

	it('should render performance stats correctly', () => {
		const mockMetrics = getMockPerformanceMetrics();

		(useApi as jest.Mock).mockReturnValue({
			data: mockMetrics,
			isLoading: false,
			error: null,
			refetch: mockRefetch,
		});

		render(<PerformanceStats />);

		// Check chart is rendered
		expect(screen.getByTestId('chart')).toBeInTheDocument();
		expect(screen.getByTestId('chart-container')).toBeInTheDocument();
		expect(screen.getByTestId('chart-grid')).toBeInTheDocument();
		expect(screen.getByTestId('chart-x-axis')).toBeInTheDocument();
		expect(screen.getByTestId('chart-y-axis')).toBeInTheDocument();

		// Check metrics are displayed
		expect(
			screen.getByText(`${mockMetrics.avgQueryTime.toFixed(2)}ms`)
		).toBeInTheDocument();
		expect(
			screen.getByText(`${mockMetrics.connectionCount}`)
		).toBeInTheDocument();
		expect(
			screen.getByText(`${mockMetrics.cacheHitRate.indexHitRate.toFixed(1)}%`)
		).toBeInTheDocument();

		// Check labels
		expect(screen.getByText('Current Query Time')).toBeInTheDocument();
		expect(screen.getByText('Current Connections')).toBeInTheDocument();
		expect(screen.getByText('Cache Hit Rate')).toBeInTheDocument();
	});

	it('should render empty state correctly', () => {
		(useApi as jest.Mock).mockReturnValue({
			data: null,
			isLoading: false,
			error: null,
			refetch: mockRefetch,
		});

		render(<PerformanceStats />);

		expect(
			screen.getByText('No performance data available')
		).toBeInTheDocument();
	});

	it('should handle refresh button correctly', async () => {
		(useApi as jest.Mock).mockReturnValue({
			data: getMockPerformanceMetrics(),
			isLoading: false,
			error: null,
			refetch: mockRefetch,
		});

		render(<PerformanceStats />);

		// Test refresh button
		fireEvent.click(screen.getByText('Refresh Statistics'));
		expect(mockRefetch).toHaveBeenCalledTimes(1);
	});

	it('should display timestamp when available', () => {
		const mockMetrics = getMockPerformanceMetrics();
		const timestamp = new Date().toISOString();
		mockMetrics.timestamp = timestamp;

		(useApi as jest.Mock).mockReturnValue({
			data: mockMetrics,
			isLoading: false,
			error: null,
			refetch: mockRefetch,
		});

		render(<PerformanceStats />);

		expect(
			screen.getByText(`Last updated: ${new Date(timestamp).toLocaleString()}`)
		).toBeInTheDocument();
	});
});
