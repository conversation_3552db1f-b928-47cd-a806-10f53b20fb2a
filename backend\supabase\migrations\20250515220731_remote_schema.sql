

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."DelegationStatus" AS ENUM (
    'Planned',
    'Confirmed',
    'In_Progress',
    'Completed',
    'Cancelled',
    'No_details'
);


ALTER TYPE "public"."DelegationStatus" OWNER TO "postgres";


CREATE TYPE "public"."DriverAvailability" AS ENUM (
    'On_Shift',
    'Off_Shift',
    'On_Break',
    'Busy'
);


ALTER TYPE "public"."DriverAvailability" OWNER TO "postgres";


CREATE TYPE "public"."EmployeeRole" AS ENUM (
    'driver',
    'mechanic',
    'administrator',
    'office_staff',
    'manager',
    'service_advisor',
    'technician',
    'other'
);


ALTER TYPE "public"."EmployeeRole" OWNER TO "postgres";


CREATE TYPE "public"."EmployeeStatus" AS ENUM (
    'Active',
    'On_Leave',
    'Terminated',
    'Inactive'
);


ALTER TYPE "public"."EmployeeStatus" OWNER TO "postgres";


CREATE TYPE "public"."TaskPriority" AS ENUM (
    'Low',
    'Medium',
    'High'
);


ALTER TYPE "public"."TaskPriority" OWNER TO "postgres";


CREATE TYPE "public"."TaskStatus" AS ENUM (
    'Pending',
    'Assigned',
    'In_Progress',
    'Completed',
    'Cancelled'
);


ALTER TYPE "public"."TaskStatus" OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."Delegate" (
    "id" "text" NOT NULL,
    "delegationId" "text" NOT NULL,
    "name" "text" NOT NULL,
    "title" "text" NOT NULL,
    "notes" "text"
);


ALTER TABLE "public"."Delegate" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."Delegation" (
    "id" "text" NOT NULL,
    "eventName" "text" NOT NULL,
    "location" "text" NOT NULL,
    "durationFrom" timestamp(3) without time zone NOT NULL,
    "durationTo" timestamp(3) without time zone NOT NULL,
    "invitationFrom" "text",
    "invitationTo" "text",
    "flightArrivalId" "text",
    "flightDepartureId" "text",
    "status" "public"."DelegationStatus" NOT NULL,
    "notes" "text",
    "imageUrl" "text",
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE "public"."Delegation" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."DelegationStatusEntry" (
    "id" "text" NOT NULL,
    "delegationId" "text" NOT NULL,
    "status" "public"."DelegationStatus" NOT NULL,
    "changedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "reason" "text"
);


ALTER TABLE "public"."DelegationStatusEntry" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."Employee" (
    "id" integer NOT NULL,
    "name" "text" NOT NULL,
    "fullName" "text",
    "role" "public"."EmployeeRole" NOT NULL,
    "employeeId" "text" NOT NULL,
    "contactInfo" "text" NOT NULL,
    "contactEmail" "text",
    "contactPhone" "text",
    "contactMobile" "text",
    "position" "text",
    "department" "text",
    "hireDate" timestamp(3) without time zone,
    "status" "public"."EmployeeStatus",
    "availability" "public"."DriverAvailability",
    "currentLocation" "text",
    "workingHours" "text",
    "assignedVehicleId" integer,
    "skills" "text"[] DEFAULT ARRAY[]::"text"[],
    "shiftSchedule" "text",
    "generalAssignments" "text"[] DEFAULT ARRAY[]::"text"[],
    "notes" "text",
    "profileImageUrl" "text",
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE "public"."Employee" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."EmployeeStatusEntry" (
    "id" "text" NOT NULL,
    "employeeId" integer NOT NULL,
    "status" "public"."EmployeeStatus" NOT NULL,
    "changedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "reason" "text"
);


ALTER TABLE "public"."EmployeeStatusEntry" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."Employee_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."Employee_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."Employee_id_seq" OWNED BY "public"."Employee"."id";



CREATE TABLE IF NOT EXISTS "public"."FlightDetails" (
    "id" "text" NOT NULL,
    "flightNumber" "text" NOT NULL,
    "dateTime" timestamp(3) without time zone NOT NULL,
    "airport" "text" NOT NULL,
    "terminal" "text",
    "notes" "text",
    "arrivalDelegationId" "text",
    "departureDelegationId" "text"
);


ALTER TABLE "public"."FlightDetails" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."ServiceRecord" (
    "id" "text" NOT NULL,
    "vehicleId" integer NOT NULL,
    "employeeId" integer,
    "date" timestamp(3) without time zone NOT NULL,
    "odometer" integer NOT NULL,
    "servicePerformed" "text"[],
    "notes" "text",
    "cost" numeric(65,30),
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE "public"."ServiceRecord" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."SubTask" (
    "id" "text" NOT NULL,
    "taskId" "text" NOT NULL,
    "title" "text" NOT NULL,
    "completed" boolean DEFAULT false NOT NULL
);


ALTER TABLE "public"."SubTask" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."Task" (
    "id" "text" NOT NULL,
    "description" "text" NOT NULL,
    "location" "text" NOT NULL,
    "dateTime" timestamp(3) without time zone NOT NULL,
    "estimatedDuration" integer NOT NULL,
    "requiredSkills" "text"[] DEFAULT ARRAY[]::"text"[],
    "priority" "public"."TaskPriority" NOT NULL,
    "deadline" timestamp(3) without time zone,
    "status" "public"."TaskStatus" NOT NULL,
    "notes" "text",
    "vehicleId" integer,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "assignedEmployeeIds" "text"[] DEFAULT ARRAY[]::"text"[]
);


ALTER TABLE "public"."Task" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."TaskStatusEntry" (
    "id" "text" NOT NULL,
    "taskId" "text" NOT NULL,
    "status" "public"."TaskStatus" NOT NULL,
    "changedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "reason" "text"
);


ALTER TABLE "public"."TaskStatusEntry" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."Vehicle" (
    "id" integer NOT NULL,
    "make" "text" NOT NULL,
    "model" "text" NOT NULL,
    "year" integer NOT NULL,
    "vin" "text" NOT NULL,
    "licensePlate" "text" NOT NULL,
    "ownerName" "text" NOT NULL,
    "ownerContact" "text" NOT NULL,
    "color" "text",
    "initialOdometer" integer,
    "imageUrl" "text",
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE "public"."Vehicle" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."Vehicle_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."Vehicle_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."Vehicle_id_seq" OWNED BY "public"."Vehicle"."id";



CREATE TABLE IF NOT EXISTS "public"."_EmployeeTasks" (
    "A" integer NOT NULL,
    "B" "text" NOT NULL
);


ALTER TABLE "public"."_EmployeeTasks" OWNER TO "postgres";


ALTER TABLE ONLY "public"."Employee" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."Employee_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."Vehicle" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."Vehicle_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."Delegate"
    ADD CONSTRAINT "Delegate_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."DelegationStatusEntry"
    ADD CONSTRAINT "DelegationStatusEntry_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."Delegation"
    ADD CONSTRAINT "Delegation_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."EmployeeStatusEntry"
    ADD CONSTRAINT "EmployeeStatusEntry_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."Employee"
    ADD CONSTRAINT "Employee_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."FlightDetails"
    ADD CONSTRAINT "FlightDetails_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."ServiceRecord"
    ADD CONSTRAINT "ServiceRecord_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."SubTask"
    ADD CONSTRAINT "SubTask_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."TaskStatusEntry"
    ADD CONSTRAINT "TaskStatusEntry_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."Task"
    ADD CONSTRAINT "Task_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."Vehicle"
    ADD CONSTRAINT "Vehicle_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_EmployeeTasks"
    ADD CONSTRAINT "_EmployeeTasks_AB_pkey" PRIMARY KEY ("A", "B");



CREATE UNIQUE INDEX "Delegation_flightArrivalId_key" ON "public"."Delegation" USING "btree" ("flightArrivalId");



CREATE UNIQUE INDEX "Delegation_flightDepartureId_key" ON "public"."Delegation" USING "btree" ("flightDepartureId");



CREATE UNIQUE INDEX "Employee_assignedVehicleId_key" ON "public"."Employee" USING "btree" ("assignedVehicleId");



CREATE UNIQUE INDEX "Employee_employeeId_key" ON "public"."Employee" USING "btree" ("employeeId");



CREATE UNIQUE INDEX "FlightDetails_arrivalDelegationId_key" ON "public"."FlightDetails" USING "btree" ("arrivalDelegationId");



CREATE UNIQUE INDEX "FlightDetails_departureDelegationId_key" ON "public"."FlightDetails" USING "btree" ("departureDelegationId");



CREATE UNIQUE INDEX "Vehicle_vin_key" ON "public"."Vehicle" USING "btree" ("vin");



CREATE INDEX "_EmployeeTasks_B_index" ON "public"."_EmployeeTasks" USING "btree" ("B");



ALTER TABLE ONLY "public"."Delegate"
    ADD CONSTRAINT "Delegate_delegationId_fkey" FOREIGN KEY ("delegationId") REFERENCES "public"."Delegation"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."DelegationStatusEntry"
    ADD CONSTRAINT "DelegationStatusEntry_delegationId_fkey" FOREIGN KEY ("delegationId") REFERENCES "public"."Delegation"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."Delegation"
    ADD CONSTRAINT "Delegation_flightArrivalId_fkey" FOREIGN KEY ("flightArrivalId") REFERENCES "public"."FlightDetails"("id") ON UPDATE CASCADE ON DELETE SET NULL;



ALTER TABLE ONLY "public"."Delegation"
    ADD CONSTRAINT "Delegation_flightDepartureId_fkey" FOREIGN KEY ("flightDepartureId") REFERENCES "public"."FlightDetails"("id") ON UPDATE CASCADE ON DELETE SET NULL;



ALTER TABLE ONLY "public"."EmployeeStatusEntry"
    ADD CONSTRAINT "EmployeeStatusEntry_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "public"."Employee"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."Employee"
    ADD CONSTRAINT "Employee_assignedVehicleId_fkey" FOREIGN KEY ("assignedVehicleId") REFERENCES "public"."Vehicle"("id") ON UPDATE CASCADE ON DELETE SET NULL;



ALTER TABLE ONLY "public"."ServiceRecord"
    ADD CONSTRAINT "ServiceRecord_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "public"."Employee"("id") ON UPDATE CASCADE ON DELETE SET NULL;



ALTER TABLE ONLY "public"."ServiceRecord"
    ADD CONSTRAINT "ServiceRecord_vehicleId_fkey" FOREIGN KEY ("vehicleId") REFERENCES "public"."Vehicle"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."SubTask"
    ADD CONSTRAINT "SubTask_taskId_fkey" FOREIGN KEY ("taskId") REFERENCES "public"."Task"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."TaskStatusEntry"
    ADD CONSTRAINT "TaskStatusEntry_taskId_fkey" FOREIGN KEY ("taskId") REFERENCES "public"."Task"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."Task"
    ADD CONSTRAINT "Task_vehicleId_fkey" FOREIGN KEY ("vehicleId") REFERENCES "public"."Vehicle"("id") ON UPDATE CASCADE ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_EmployeeTasks"
    ADD CONSTRAINT "_EmployeeTasks_A_fkey" FOREIGN KEY ("A") REFERENCES "public"."Employee"("id") ON UPDATE CASCADE ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_EmployeeTasks"
    ADD CONSTRAINT "_EmployeeTasks_B_fkey" FOREIGN KEY ("B") REFERENCES "public"."Task"("id") ON UPDATE CASCADE ON DELETE CASCADE;





ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";


GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";


























































































































































































GRANT ALL ON TABLE "public"."Delegate" TO "anon";
GRANT ALL ON TABLE "public"."Delegate" TO "authenticated";
GRANT ALL ON TABLE "public"."Delegate" TO "service_role";



GRANT ALL ON TABLE "public"."Delegation" TO "anon";
GRANT ALL ON TABLE "public"."Delegation" TO "authenticated";
GRANT ALL ON TABLE "public"."Delegation" TO "service_role";



GRANT ALL ON TABLE "public"."DelegationStatusEntry" TO "anon";
GRANT ALL ON TABLE "public"."DelegationStatusEntry" TO "authenticated";
GRANT ALL ON TABLE "public"."DelegationStatusEntry" TO "service_role";



GRANT ALL ON TABLE "public"."Employee" TO "anon";
GRANT ALL ON TABLE "public"."Employee" TO "authenticated";
GRANT ALL ON TABLE "public"."Employee" TO "service_role";



GRANT ALL ON TABLE "public"."EmployeeStatusEntry" TO "anon";
GRANT ALL ON TABLE "public"."EmployeeStatusEntry" TO "authenticated";
GRANT ALL ON TABLE "public"."EmployeeStatusEntry" TO "service_role";



GRANT ALL ON SEQUENCE "public"."Employee_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."Employee_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."Employee_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."FlightDetails" TO "anon";
GRANT ALL ON TABLE "public"."FlightDetails" TO "authenticated";
GRANT ALL ON TABLE "public"."FlightDetails" TO "service_role";



GRANT ALL ON TABLE "public"."ServiceRecord" TO "anon";
GRANT ALL ON TABLE "public"."ServiceRecord" TO "authenticated";
GRANT ALL ON TABLE "public"."ServiceRecord" TO "service_role";



GRANT ALL ON TABLE "public"."SubTask" TO "anon";
GRANT ALL ON TABLE "public"."SubTask" TO "authenticated";
GRANT ALL ON TABLE "public"."SubTask" TO "service_role";



GRANT ALL ON TABLE "public"."Task" TO "anon";
GRANT ALL ON TABLE "public"."Task" TO "authenticated";
GRANT ALL ON TABLE "public"."Task" TO "service_role";



GRANT ALL ON TABLE "public"."TaskStatusEntry" TO "anon";
GRANT ALL ON TABLE "public"."TaskStatusEntry" TO "authenticated";
GRANT ALL ON TABLE "public"."TaskStatusEntry" TO "service_role";



GRANT ALL ON TABLE "public"."Vehicle" TO "anon";
GRANT ALL ON TABLE "public"."Vehicle" TO "authenticated";
GRANT ALL ON TABLE "public"."Vehicle" TO "service_role";



GRANT ALL ON SEQUENCE "public"."Vehicle_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."Vehicle_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."Vehicle_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_EmployeeTasks" TO "anon";
GRANT ALL ON TABLE "public"."_EmployeeTasks" TO "authenticated";
GRANT ALL ON TABLE "public"."_EmployeeTasks" TO "service_role";









ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
