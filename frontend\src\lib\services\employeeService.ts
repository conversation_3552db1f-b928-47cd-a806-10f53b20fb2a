/**
 * Employee API service
 */
import {api, ApiRequestOptions, extractApiData} from './apiService';
import {Employee} from '../types';
import {ApiResponse} from '../types/api';

// Employee response types
export interface EmployeeResponse extends ApiResponse<Employee> {}
export interface EmployeesResponse extends ApiResponse<Employee[]> {}

// Enriched employee type
export interface EnrichedEmployee extends Employee {
	assignedVehicleDetails?: {
		id: number;
		make: string;
		model: string;
		year: number;
		licensePlate: string;
		color?: string;
	} | null;
}

export interface EnrichedEmployeesResponse
	extends ApiResponse<EnrichedEmployee[]> {}

// Employee create/update input types
export interface EmployeeCreateInput {
	name: string;
	fullName?: string;
	role: string;
	employeeId: string;
	contactInfo: string;
	contactEmail?: string;
	contactPhone?: string;
	contactMobile?: string;
	position?: string;
	department?: string;
	hireDate: string;
	status?: string;
	availability?: string;
	currentLocation?: string;
	workingHours?: string;
	assignedVehicleId?: number | null;
	skills?: string[];
	shiftSchedule?: string;
	generalAssignments?: string[];
	notes?: string;
	profileImageUrl?: string;
}

export interface EmployeeUpdateInput extends Partial<EmployeeCreateInput> {
	statusChangeReason?: string;
}

/**
 * Get all employees
 */
export async function getEmployees(
	options?: ApiRequestOptions
): Promise<Employee[]> {
	try {
		const response = await api.get<Employee[] | EmployeesResponse>(
			'/employees',
			options
		);
		return extractApiData<Employee[]>(response, '/employees') || [];
	} catch (error) {
		console.error('Error fetching employees:', error);
		throw error;
	}
}

/**
 * Get all employees enriched with vehicle information
 */
export async function getEnrichedEmployees(
	options?: ApiRequestOptions
): Promise<EnrichedEmployee[]> {
	try {
		const response = await api.get<
			EnrichedEmployee[] | EnrichedEmployeesResponse
		>('/employees/enriched', options);
		return (
			extractApiData<EnrichedEmployee[]>(response, '/employees/enriched') || []
		);
	} catch (error) {
		console.error('Error fetching enriched employees:', error);
		throw error;
	}
}

/**
 * Get employee by ID
 */
export async function getEmployeeById(
	id: number,
	options?: ApiRequestOptions
): Promise<Employee | null> {
	try {
		const response = await api.get<Employee | EmployeeResponse>(
			`/employees/${id}`,
			options
		);
		return extractApiData<Employee>(response, `/employees/${id}`) || null;
	} catch (error) {
		console.error(`Error fetching employee with ID ${id}:`, error);
		throw error;
	}
}

/**
 * Create a new employee
 */
export async function createEmployee(
	data: EmployeeCreateInput,
	options?: ApiRequestOptions
): Promise<Employee | null> {
	try {
		const response = await api.post<Employee | EmployeeResponse>(
			'/employees',
			data,
			options
		);
		return extractApiData<Employee>(response, '/employees (POST)') || null;
	} catch (error) {
		console.error('Error creating employee:', error);
		throw error;
	}
}

/**
 * Update an existing employee
 */
export async function updateEmployee(
	id: number,
	data: EmployeeUpdateInput,
	options?: ApiRequestOptions
): Promise<Employee | null> {
	try {
		const response = await api.put<Employee | EmployeeResponse>(
			`/employees/${id}`,
			data,
			options
		);
		return extractApiData<Employee>(response, `/employees/${id} (PUT)`) || null;
	} catch (error) {
		console.error(`Error updating employee with ID ${id}:`, error);
		throw error;
	}
}

/**
 * Delete an employee
 */
export async function deleteEmployee(
	id: number,
	options?: ApiRequestOptions
): Promise<boolean> {
	try {
		await api.delete(`/employees/${id}`, options);
		return true;
	} catch (error) {
		console.error(`Error deleting employee with ID ${id}:`, error);
		throw error;
	}
}
