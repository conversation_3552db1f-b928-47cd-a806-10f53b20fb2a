/**
 * Enhanced API service with TypeScript types, error handling, and retry logic
 * 🚨 EMERGENCY SECURITY: Now includes JWT token authentication
 */
import {
	ApiResponse,
	ApiError,
	ApiRequestOptions,
	ApiValidationError,
	QueryParams,
	ApiErrorType,
} from '../types/api';

// Global token storage for API requests
let globalAuthToken: string | null = null;

/**
 * Set the global authentication token for API requests
 * This should be called whenever the user's session changes
 */
export function setAuthToken(token: string | null) {
	globalAuthToken = token;
	if (process.env.NODE_ENV !== 'production') {
		console.log(
			'🔐 API Service: Auth token updated',
			token ? 'Token set' : 'Token cleared'
		);
	}
}

/**
 * Get the current authentication token
 */
export function getAuthToken(): string | null {
	return globalAuthToken;
}

// For local development, use localhost
const isDockerEnvironment =
	typeof window !== 'undefined' &&
	window.location.hostname !== 'localhost' &&
	window.location.hostname !== '127.0.0.1';

// Try environment variable first, then Docker service name, then localhost fallback
export const API_BASE_URL =
	process.env.NEXT_PUBLIC_API_BASE_URL ||
	(isDockerEnvironment
		? 'http://backend:3001/api'
		: 'http://localhost:3001/api');

// Log the API base URL for debugging (only in development)
if (process.env.NODE_ENV !== 'production' && typeof window !== 'undefined') {
	console.log(`[API Service] Using API base URL: ${API_BASE_URL}`);
	console.log(
		`[API Service] Environment: ${isDockerEnvironment ? 'Docker' : 'Local'}`
	);
}

/**
 * Get default request options with authentication header
 */
function getDefaultOptions(): ApiRequestOptions {
	const headers: Record<string, string> = {
		'Content-Type': 'application/json',
	};

	// Add Authorization header if token is available
	if (globalAuthToken) {
		headers['Authorization'] = `Bearer ${globalAuthToken}`;
	}

	return {
		headers,
		retries: 3,
		retryDelay: 1000, // 1 second
		timeout: 10000, // 10 seconds
	};
}

/**
 * Sleep utility for retry delays
 */
const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

/**
 * Calculate exponential backoff delay
 */
const getRetryDelay = (attempt: number, baseDelay: number) => {
	return Math.min(
		baseDelay * Math.pow(2, attempt), // Exponential backoff
		30000 // Max 30 seconds
	);
};

/**
 * Get error type from HTTP status code
 */
function getErrorTypeFromStatus(status: number): ApiErrorType {
	if (status >= 500) {
		return ApiErrorType.SERVER_ERROR;
	} else if (status === 429) {
		return ApiErrorType.RATE_LIMIT;
	} else if (status === 401) {
		return ApiErrorType.AUTHENTICATION_ERROR;
	} else if (status === 403) {
		return ApiErrorType.AUTHORIZATION_ERROR;
	} else if (status === 404) {
		return ApiErrorType.NOT_FOUND;
	} else if (status >= 400 && status < 500) {
		return ApiErrorType.CLIENT_ERROR;
	} else {
		return ApiErrorType.UNKNOWN;
	}
}

/**
 * Process API response with enhanced error handling
 */
async function processResponse<T>(response: Response): Promise<T> {
	// Handle 204 No Content
	if (response.status === 204) {
		return null as unknown as T;
	}

	// For successful responses, parse and return the data
	if (response.ok) {
		try {
			const data = await response.json();
			return data as T;
		} catch (error) {
			// Log the error with response details
			console.error('Failed to parse successful API response:', {
				status: response.status,
				statusText: response.statusText,
				url: response.url,
				error: error instanceof Error ? error.message : 'Unknown error',
			});

			throw new ApiError(
				`Failed to parse successful response: ${
					error instanceof Error ? error.message : 'Unknown error'
				}`,
				{
					status: response.status,
					endpoint: response.url,
					errorType: ApiErrorType.PARSING_ERROR,
				}
			);
		}
	}

	// Handle error responses
	let errorData: any;
	let errorType: ApiErrorType = getErrorTypeFromStatus(response.status);
	let errorMessage = `HTTP error ${response.status}`;
	let errorDetails: any = undefined;

	// Try to parse error response body
	try {
		errorData = await response.json();
		errorMessage = errorData?.message || errorMessage;
		errorDetails = errorData?.details;

		// Special handling for validation errors
		if (
			response.status === 400 &&
			errorData?.status === 'error' &&
			errorData?.message === 'Validation failed'
		) {
			errorType = ApiErrorType.VALIDATION_ERROR;
			const validationErrors = errorData.errors as ApiValidationError[];

			// Log validation errors with context
			console.error('API validation errors:', {
				endpoint: response.url,
				status: response.status,
				errors: validationErrors,
				receivedData: errorData.receivedData,
			});

			throw new ApiError(errorData.message, {
				status: response.status,
				endpoint: response.url,
				errorType,
				validationErrors,
				receivedData: errorData.receivedData,
			});
		}
	} catch (parseError) {
		// If we can't parse JSON, try to get text content
		try {
			if (!(parseError instanceof ApiError)) {
				// Skip if we already created an ApiError
				const textContent = await response.text();
				if (textContent) {
					errorMessage = textContent;
				}
			} else {
				throw parseError; // Re-throw ApiError
			}
		} catch (textError) {
			// If we can't get text either, use status text
			if (!(textError instanceof ApiError)) {
				// Skip if we already created an ApiError
				errorMessage = response.statusText || errorMessage;
			} else {
				throw textError; // Re-throw ApiError
			}
		}
	}

	// Log error with context
	console.error(`API error (${response.status}):`, {
		endpoint: response.url,
		status: response.status,
		message: errorMessage,
		details: errorDetails,
		errorType,
	});

	// Create and throw detailed error
	throw new ApiError(errorMessage, {
		status: response.status,
		endpoint: response.url,
		details: errorDetails,
		errorType,
	});
}

/**
 * Build URL with query parameters
 */
export function buildUrl(path: string, params?: QueryParams): string {
	if (!params) return path;

	const url = new URL(
		path,
		typeof window !== 'undefined' ? window.location.origin : undefined
	);

	Object.entries(params).forEach(([key, value]) => {
		if (value !== undefined && value !== null) {
			url.searchParams.append(key, String(value));
		}
	});

	return url.pathname + url.search;
}

/**
 * Enhanced fetch with retries, timeouts, and error handling
 */
export async function fetchWithRetry<T>(
	url: string,
	options: ApiRequestOptions = {}
): Promise<T> {
	const defaultOptions = getDefaultOptions();
	const mergedOptions: ApiRequestOptions = {
		...defaultOptions,
		...options,
		// Merge headers properly to preserve auth token
		headers: {
			...defaultOptions.headers,
			...options.headers,
		},
	};

	const {
		retries = 3,
		retryDelay = 1000,
		timeout = 10000,
		skipRetryLogging = false,
	} = mergedOptions;

	// Log outgoing request in development
	if (process.env.NODE_ENV !== 'production') {
		console.debug(`API Request: ${mergedOptions.method || 'GET'} ${url}`);
		if (mergedOptions.body) {
			try {
				console.debug(
					'Request payload:',
					JSON.parse(mergedOptions.body as string)
				);
			} catch (e) {
				console.debug('Request payload (non-JSON):', mergedOptions.body);
			}
		}
	}

	// Create a more descriptive error object for better debugging
	const createDetailedError = (
		status: number,
		endpoint: string,
		message: string,
		details?: any
	): ApiError => {
		const errorType = getErrorTypeFromStatus(status);
		const error = new ApiError(message, {
			endpoint,
			status,
			errorType,
			details,
		});
		return error;
	};

	let lastError: Error | null = null;

	for (let attempt = 0; attempt <= retries; attempt++) {
		try {
			// Create AbortController for timeout
			const controller = new AbortController();
			const timeoutId = setTimeout(() => {
				controller.abort();
			}, timeout);

			// Add signal to options
			const fetchOptions = {
				...mergedOptions,
				signal: controller.signal,
			};

			// Execute fetch - handle both relative and absolute URLs
			const fullUrl = url.startsWith('http') ? url : `${API_BASE_URL}${url}`;
			const response = await fetch(fullUrl, fetchOptions);

			// Clear timeout
			clearTimeout(timeoutId);

			// Process response
			const data = await processResponse<T>(response);

			// Log successful response in development
			if (process.env.NODE_ENV !== 'production') {
				console.debug(`API Response from ${url}:`, data);
			}

			return data;
		} catch (error) {
			// Store the error
			lastError = error as Error;

			// Categorize the error
			const errorType = categorizeError(error);

			// Don't retry client errors (4xx) except for 429 (Too Many Requests)
			if (errorType === ApiErrorType.CLIENT_ERROR) {
				throw error;
			}

			// Don't retry if this was the last attempt
			if (attempt === retries) {
				throw error;
			}

			// Calculate retry delay with exponential backoff
			const delay = getRetryDelay(attempt, retryDelay);

			// Log retry attempt (unless explicitly disabled)
			if (!skipRetryLogging) {
				console.warn(
					`API request failed (attempt ${attempt + 1}/${
						retries + 1
					}), retrying in ${delay / 1000}s:`,
					error
				);
			}

			// Wait before retrying
			await sleep(delay);
		}
	}

	// This should never be reached due to the throw in the loop
	throw lastError || new Error('Failed to fetch after retries');
}

/**
 * Categorize errors to determine retry strategy
 */
function categorizeError(error: unknown): ApiErrorType {
	// Handle API errors with status codes
	if (error instanceof ApiError) {
		if (error.status >= 400 && error.status < 500) {
			// Don't retry 4xx errors except 429 (Too Many Requests)
			return error.status === 429
				? ApiErrorType.RATE_LIMIT
				: ApiErrorType.CLIENT_ERROR;
		}
		if (error.status >= 500) {
			return ApiErrorType.SERVER_ERROR;
		}
	}

	// Handle network errors
	if (error instanceof TypeError && error.message.includes('network')) {
		return ApiErrorType.NETWORK_ERROR;
	}

	// Handle timeout errors
	if (error instanceof DOMException && error.name === 'AbortError') {
		return ApiErrorType.TIMEOUT;
	}

	// Default to unknown error
	return ApiErrorType.UNKNOWN;
}

/**
 * Generic API request function
 */
export async function apiRequest<T>(
	method: string,
	path: string,
	data?: any,
	options: ApiRequestOptions = {}
): Promise<T> {
	const requestOptions: ApiRequestOptions = {
		...options,
		method,
	};

	// Add body for non-GET requests
	if (method !== 'GET' && data) {
		requestOptions.body = JSON.stringify(data);
	}

	return fetchWithRetry<T>(path, requestOptions);
}

/**
 * Utility function to extract data from API responses that may be in different formats
 * Handles both direct data responses and wrapped ApiResponse objects
 *
 * @param response The API response which could be either direct data or a wrapped ApiResponse
 * @param endpoint The API endpoint for logging purposes
 * @returns The extracted data
 */
export function extractApiData<T>(response: any, endpoint?: string): T {
	// Handle both response formats:
	// 1. Direct data: T (array or object)
	// 2. Wrapped in ApiResponse: { status: 'success', data: T }

	if (response && typeof response === 'object') {
		// Check if it's a wrapped ApiResponse with data property
		if ('data' in response && 'status' in response) {
			if (process.env.NODE_ENV !== 'production') {
				console.debug(
					`Received wrapped ApiResponse format from ${endpoint || 'API'}`
				);
			}
			return response.data as T;
		}

		// For arrays, check if it's a direct array response
		if (Array.isArray(response)) {
			if (process.env.NODE_ENV !== 'production') {
				console.debug(
					`Received direct array response format from ${endpoint || 'API'}`
				);
			}
			return response as T;
		}

		// For objects, check if it has expected properties of the target type
		// This is a heuristic and may need adjustment based on your data types
		if (
			!('status' in response) &&
			!('message' in response) &&
			!('errors' in response)
		) {
			if (process.env.NODE_ENV !== 'production') {
				console.debug(
					`Received direct object response format from ${endpoint || 'API'}`
				);
			}
			return response as T;
		}
	}

	// If we can't determine the format or it's null/undefined, return the response as is
	// This could be null, undefined, or some other unexpected format
	if (process.env.NODE_ENV !== 'production') {
		console.warn(
			`Unexpected or empty response format from ${endpoint || 'API'}:`,
			response
		);
	}
	return response as T;
}

/**
 * HTTP method wrappers
 */
export const api = {
	get: <T>(path: string, options?: ApiRequestOptions): Promise<T> =>
		apiRequest<T>('GET', path, undefined, options),

	post: <T>(
		path: string,
		data?: any,
		options?: ApiRequestOptions
	): Promise<T> => apiRequest<T>('POST', path, data, options),

	put: <T>(path: string, data?: any, options?: ApiRequestOptions): Promise<T> =>
		apiRequest<T>('PUT', path, data, options),

	patch: <T>(
		path: string,
		data?: any,
		options?: ApiRequestOptions
	): Promise<T> => apiRequest<T>('PATCH', path, data, options),

	delete: <T>(path: string, options?: ApiRequestOptions): Promise<T> =>
		apiRequest<T>('DELETE', path, undefined, options),
};
