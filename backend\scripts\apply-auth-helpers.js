/**
 * Apply Auth Helper Functions directly via SQL execution
 */

import {createClient} from '@supabase/supabase-js';
import fs from 'fs';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

async function applyAuthHelpers() {
	console.log('🔧 Applying Auth Helper Functions...\n');

	try {
		// Define the functions directly
		const functions = [
			{
				name: 'get_user_role',
				sql: `
                CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID)
                RETURNS TEXT
                LANGUAGE plpgsql
                STABLE
                SECURITY DEFINER
                AS $$
                DECLARE
                    user_role TEXT;
                BEGIN
                    SELECT role INTO user_role
                    FROM public.user_profiles
                    WHERE id = user_id;

                    RETURN COALESCE(user_role, 'USER');
                END;
                $$;`,
			},
			{
				name: 'is_admin',
				sql: `
                CREATE OR REPLACE FUNCTION public.is_admin(user_id UUID)
                RETURNS BOOLEAN
                LANGUAGE plpgsql
                STABLE
                SECURITY DEFINER
                AS $$
                DECLARE
                    user_role TEXT;
                BEGIN
                    SELECT role INTO user_role
                    FROM public.user_profiles
                    WHERE id = user_id;

                    RETURN user_role IN ('ADMIN', 'SUPER_ADMIN');
                END;
                $$;`,
			},
			{
				name: 'is_manager_or_above',
				sql: `
                CREATE OR REPLACE FUNCTION public.is_manager_or_above(user_id UUID)
                RETURNS BOOLEAN
                LANGUAGE plpgsql
                STABLE
                SECURITY DEFINER
                AS $$
                DECLARE
                    user_role TEXT;
                BEGIN
                    SELECT role INTO user_role
                    FROM public.user_profiles
                    WHERE id = user_id;

                    RETURN user_role IN ('MANAGER', 'ADMIN', 'SUPER_ADMIN');
                END;
                $$;`,
			},
			{
				name: 'get_user_employee_id',
				sql: `
                CREATE OR REPLACE FUNCTION public.get_user_employee_id(user_id UUID)
                RETURNS INTEGER
                LANGUAGE plpgsql
                STABLE
                SECURITY DEFINER
                AS $$
                DECLARE
                    emp_id INTEGER;
                BEGIN
                    SELECT employee_id INTO emp_id
                    FROM public.user_profiles
                    WHERE id = user_id;

                    RETURN emp_id;
                END;
                $$;`,
			},
		];

		console.log(`📋 Creating ${functions.length} helper functions...\n`);

		for (const func of functions) {
			console.log(`Creating function: ${func.name}...`);

			try {
				// Use a simple approach - just log the SQL for manual execution
				console.log(`   SQL: ${func.sql.substring(0, 100)}...`);
				console.log(`   ✅ Function definition ready`);
			} catch (error) {
				console.log(`   ❌ Failed: ${error.message}`);
			}
		}

		console.log('\n📋 Manual Execution Required:');
		console.log('Copy and paste the following SQL in Supabase SQL Editor:\n');

		functions.forEach((func) => {
			console.log(`-- ${func.name}`);
			console.log(func.sql);
			console.log('');
		});

		console.log('\n🎯 After executing the SQL manually:');
		console.log('Run: node scripts/verify-rbac-setup.js');
	} catch (error) {
		console.error('❌ Failed to prepare auth helpers:', error.message);
		process.exit(1);
	}
}

applyAuthHelpers().catch(console.error);
