import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import {ViewReportButton} from '../ViewReportButton';

// Mock window.open
const mockOpen = jest.fn();
window.open = mockOpen;

// We're using the global mock for next/link from setupTests.ts

describe('ViewReportButton', () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	it('renders correctly with href prop (individual report)', () => {
		render(<ViewReportButton href='/vehicles/123/report' />);

		// Check button text
		expect(screen.getByText('View Report')).toBeInTheDocument();

		// Check link attributes
		const link = screen.getByRole('link');
		expect(link).toHaveAttribute('href', '/vehicles/123/report');
		expect(link).toHaveAttribute('target', '_blank');
		expect(link).toHaveAttribute('rel', 'noopener noreferrer');

		// Check accessibility text
		expect(screen.getByText('(opens in new tab)')).toBeInTheDocument();
	});

	it('renders correctly with isList=true and href prop', () => {
		render(<ViewReportButton href='/delegations/report/list' isList={true} />);

		// Check button text for list report
		expect(screen.getByText('View List Report')).toBeInTheDocument();
	});

	it('calls window.open with correct URL when using getReportUrl prop', () => {
		const mockGetReportUrl = jest
			.fn()
			.mockReturnValue('/tasks/report?status=pending');

		render(<ViewReportButton getReportUrl={mockGetReportUrl} isList={true} />);

		// Click the button
		fireEvent.click(screen.getByText('View List Report'));

		// Check if getReportUrl was called
		expect(mockGetReportUrl).toHaveBeenCalledTimes(1);

		// Check if window.open was called with correct parameters
		expect(mockOpen).toHaveBeenCalledWith(
			'/tasks/report?status=pending',
			'_blank',
			'noopener,noreferrer'
		);
	});

	it('logs error and returns null when neither href nor getReportUrl is provided', () => {
		const consoleSpy = jest
			.spyOn(console, 'error')
			.mockImplementation(() => {});

		const {container} = render(<ViewReportButton />);

		expect(consoleSpy).toHaveBeenCalledWith(
			'ViewReportButton requires either href or getReportUrl prop'
		);
		expect(container.firstChild).toBeNull();

		consoleSpy.mockRestore();
	});
});
