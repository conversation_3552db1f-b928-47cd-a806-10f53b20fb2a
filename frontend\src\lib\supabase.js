import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
	throw new Error('Missing Supabase environment variables. Please check NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY in your .env.local file');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Test connection function for frontend
export const testSupabaseConnection = async () => {
	try {
		const { data, error } = await supabase.auth.getSession();
		
		if (error) {
			console.error('❌ Frontend Supabase connection test failed:', error.message);
			return false;
		}
		
		console.log('✅ Frontend Supabase connection successful');
		return true;
	} catch (error) {
		console.error('❌ Frontend Supabase connection test error:', error.message);
		return false;
	}
};
