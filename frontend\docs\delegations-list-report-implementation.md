# Delegations List Report Implementation Plan

This document provides a detailed implementation plan for redesigning the Delegations List Report page in the WorkHub application.

## Current Implementation Analysis

The current Delegations List Report (`/delegations/report/list`) has the following structure:

1. **Header Section**
   - Title: "Delegation List Report"
   - Subtitle: Shows search term if present, otherwise "All Delegations"

2. **Action Bar**
   - Uses `ReportActions` component for print/download functionality
   - Positioned in the top-right corner

3. **Content Area**
   - Uses a `Card` component with `Table` for data presentation
   - Displays delegation information in columns: Event Name, Location, Duration, Status, Delegates
   - Status is displayed with a `Badge` component with color coding
   - No pagination for large data sets

4. **Footer**
   - Shows report generation date
   - Shows application branding

## Redesign Implementation

The redesign will be implemented in multiple phases to ensure manageable changes:

### Phase 1: Enhanced Layout Structure

1. **Create a Summary Statistics Component**

```tsx
// components/reports/DelegationSummary.tsx
import React from 'react';
import type { Delegation } from '@/lib/types';

interface DelegationSummaryProps {
  delegations: Delegation[];
}

export function DelegationSummary({ delegations }: DelegationSummaryProps) {
  // Calculate statistics
  const total = delegations.length;
  
  const statusCounts = delegations.reduce((acc, delegation) => {
    const status = delegation.status;
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  
  const totalDelegates = delegations.reduce((acc, delegation) => {
    return acc + delegation.delegates.length;
  }, 0);
  
  return (
    <div className="mt-4 grid grid-cols-2 sm:grid-cols-4 gap-2">
      <div className="bg-gray-50 p-2 rounded">
        <p className="text-2xl font-semibold">{total}</p>
        <p className="text-xs text-gray-500">Total Delegations</p>
      </div>
      
      <div className="bg-blue-50 p-2 rounded">
        <p className="text-2xl font-semibold">{statusCounts['Planned'] || 0}</p>
        <p className="text-xs text-blue-500">Planned</p>
      </div>
      
      <div className="bg-yellow-50 p-2 rounded">
        <p className="text-2xl font-semibold">{statusCounts['In_Progress'] || 0}</p>
        <p className="text-xs text-yellow-500">In Progress</p>
      </div>
      
      <div className="bg-green-50 p-2 rounded">
        <p className="text-2xl font-semibold">{statusCounts['Completed'] || 0}</p>
        <p className="text-xs text-green-500">Completed</p>
      </div>
      
      <div className="bg-purple-50 p-2 rounded col-span-2">
        <p className="text-2xl font-semibold">{totalDelegates}</p>
        <p className="text-xs text-purple-500">Total Delegates</p>
      </div>
      
      <div className="bg-red-50 p-2 rounded col-span-2">
        <p className="text-2xl font-semibold">{statusCounts['Cancelled'] || 0}</p>
        <p className="text-xs text-red-500">Cancelled</p>
      </div>
    </div>
  );
}
```

2. **Create a Pagination Component**

```tsx
// components/ui/pagination.tsx
import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

interface PaginationControlsProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export function PaginationControls({
  currentPage,
  totalPages,
  onPageChange,
}: PaginationControlsProps) {
  // Calculate which page numbers to show
  const getPageNumbers = () => {
    const pageNumbers = [];
    
    // Always show first page
    pageNumbers.push(1);
    
    // Calculate range around current page
    const rangeStart = Math.max(2, currentPage - 1);
    const rangeEnd = Math.min(totalPages - 1, currentPage + 1);
    
    // Add ellipsis after first page if needed
    if (rangeStart > 2) {
      pageNumbers.push('ellipsis1');
    }
    
    // Add pages in range
    for (let i = rangeStart; i <= rangeEnd; i++) {
      pageNumbers.push(i);
    }
    
    // Add ellipsis before last page if needed
    if (rangeEnd < totalPages - 1) {
      pageNumbers.push('ellipsis2');
    }
    
    // Always show last page if there is more than one page
    if (totalPages > 1) {
      pageNumbers.push(totalPages);
    }
    
    return pageNumbers;
  };
  
  const pageNumbers = getPageNumbers();
  
  return (
    <Pagination>
      <PaginationContent>
        <PaginationItem>
          <PaginationPrevious 
            onClick={() => onPageChange(currentPage - 1)}
            className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
          />
        </PaginationItem>
        
        {pageNumbers.map((page, index) => {
          if (page === 'ellipsis1' || page === 'ellipsis2') {
            return (
              <PaginationItem key={`ellipsis-${index}`}>
                <PaginationEllipsis />
              </PaginationItem>
            );
          }
          
          return (
            <PaginationItem key={`page-${page}`}>
              <PaginationLink
                onClick={() => onPageChange(page as number)}
                isActive={currentPage === page}
                className="cursor-pointer"
              >
                {page}
              </PaginationLink>
            </PaginationItem>
          );
        })}
        
        <PaginationItem>
          <PaginationNext 
            onClick={() => onPageChange(currentPage + 1)}
            className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
}
```

### Phase 2: Update the Delegations List Report Page

```tsx
// app/delegations/report/list/page.tsx
'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import type { Delegation, Delegate } from '@/lib/types';
import { getDelegations } from '@/lib/store';
import {
  CalendarDays,
  MapPin,
  Users as UsersIcon,
  Briefcase,
  Search,
  X,
} from 'lucide-react';
import { ReportActions } from '@/components/reports/ReportActions';
import { format, parseISO } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { formatDelegationStatusForDisplay } from '@/lib/utils/formattingUtils';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent } from '@/components/ui/card';
import { SkeletonLoader } from '@/components/ui/loading';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DelegationSummary } from '@/components/reports/DelegationSummary';
import { PaginationControls } from '@/components/ui/pagination';

// Status color function (unchanged)
const getStatusColor = (status: Delegation['status']) => {
  switch (status) {
    case 'Planned':
      return 'bg-blue-100 text-blue-800 border-blue-300';
    case 'Confirmed':
      return 'bg-green-100 text-green-800 border-green-300';
    case 'In_Progress':
      return 'bg-yellow-100 text-yellow-800 border-yellow-300';
    case 'Completed':
      return 'bg-purple-100 text-purple-800 border-purple-300';
    case 'Cancelled':
      return 'bg-red-100 text-red-800 border-red-300';
    case 'No_details':
    default:
      return 'bg-gray-100 text-gray-800 border-gray-300';
  }
};

// Date formatting function (unchanged)
const formatDate = (dateString: string | undefined): string => {
  if (!dateString) return 'N/A';
  try {
    return format(parseISO(dateString), 'MMM d, yyyy');
  } catch (e) {
    return 'Invalid Date';
  }
};

// Status options for filter
const STATUS_OPTIONS = [
  'Planned',
  'Confirmed',
  'In_Progress',
  'Completed',
  'Cancelled',
  'No_details',
];

function DelegationListReportContent() {
  const searchParams = useSearchParams();
  const [allDelegations, setAllDelegations] = useState<Delegation[]>([]);
  const [filteredDelegations, setFilteredDelegations] = useState<Delegation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // Filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [dateRange, setDateRange] = useState<{ from?: Date; to?: Date }>({});
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  
  // Sorting state
  const [sortField, setSortField] = useState<string>('durationFrom');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Initialize from URL params
  useEffect(() => {
    const urlSearchTerm = searchParams.get('searchTerm') || '';
    const urlStatus = searchParams.get('status') || 'all';
    
    setSearchTerm(urlSearchTerm);
    setSelectedStatus(urlStatus);
  }, [searchParams]);

  const fetchDelegations = async () => {
    setIsLoading(true);
    try {
      document.title = 'Delegation List Report';
      const delegations = await getDelegations();
      setAllDelegations(delegations);
      applyFilters(delegations);
    } catch (error) {
      console.error('Error fetching delegations for report:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Apply filters to delegations
  const applyFilters = (delegations: Delegation[]) => {
    let tempDelegations = [...delegations];
    
    // Apply search filter
    if (searchTerm) {
      const lowercasedSearch = searchTerm.toLowerCase();
      tempDelegations = tempDelegations.filter((delegation) => {
        return (
          delegation.eventName.toLowerCase().includes(lowercasedSearch) ||
          delegation.location.toLowerCase().includes(lowercasedSearch) ||
          delegation.delegates.some((d) =>
            d.name.toLowerCase().includes(lowercasedSearch)
          ) ||
          (delegation.notes &&
            delegation.notes.toLowerCase().includes(lowercasedSearch)) ||
          delegation.status.toLowerCase().includes(lowercasedSearch)
        );
      });
    }
    
    // Apply status filter
    if (selectedStatus !== 'all') {
      tempDelegations = tempDelegations.filter(
        (delegation) => delegation.status === selectedStatus
      );
    }
    
    // Apply date range filter
    if (dateRange.from) {
      tempDelegations = tempDelegations.filter((delegation) => {
        const delegationDate = new Date(delegation.durationFrom);
        return delegationDate >= dateRange.from!;
      });
    }
    
    if (dateRange.to) {
      tempDelegations = tempDelegations.filter((delegation) => {
        const delegationDate = new Date(delegation.durationFrom);
        return delegationDate <= dateRange.to!;
      });
    }
    
    // Apply sorting
    tempDelegations = sortDelegations(tempDelegations, sortField, sortDirection);
    
    setFilteredDelegations(tempDelegations);
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Sort delegations
  const sortDelegations = (delegations: Delegation[], field: string, direction: 'asc' | 'desc') => {
    return [...delegations].sort((a, b) => {
      let valueA, valueB;
      
      // Handle different field types
      switch (field) {
        case 'durationFrom':
          valueA = new Date(a.durationFrom).getTime();
          valueB = new Date(b.durationFrom).getTime();
          break;
        case 'status':
          valueA = a.status;
          valueB = b.status;
          break;
        case 'eventName':
          valueA = a.eventName;
          valueB = b.eventName;
          break;
        case 'location':
          valueA = a.location;
          valueB = b.location;
          break;
        default:
          valueA = a[field as keyof Delegation];
          valueB = b[field as keyof Delegation];
      }
      
      // Compare values based on direction
      if (valueA < valueB) return direction === 'asc' ? -1 : 1;
      if (valueA > valueB) return direction === 'asc' ? 1 : -1;
      return 0;
    });
  };

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Reset filters
  const resetFilters = () => {
    setSearchTerm('');
    setSelectedStatus('all');
    setDateRange({});
    setCurrentPage(1);
    applyFilters(allDelegations);
  };

  // Effect to apply filters when they change
  useEffect(() => {
    if (allDelegations.length > 0) {
      applyFilters(allDelegations);
    }
  }, [searchTerm, selectedStatus, dateRange, sortField, sortDirection]);

  // Initial data fetch
  useEffect(() => {
    fetchDelegations();
  }, []);

  // Pagination logic
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const paginatedDelegations = filteredDelegations.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredDelegations.length / itemsPerPage);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  if (isLoading) {
    return (
      <div className='max-w-5xl mx-auto p-4'>
        <SkeletonLoader variant='table' count={5} />
      </div>
    );
  }

  return (
    <div className='max-w-5xl mx-auto bg-white p-2 sm:p-4 text-gray-800'>
      <div className='text-right mb-4 no-print'>
        <ReportActions
          reportContentId='#delegations-list-report-content'
          tableId='#delegations-table'
          fileName={`delegations-list-report-${
            new Date().toISOString().split('T')[0]
          }`}
          enableCsv={filteredDelegations.length > 0}
        />
      </div>

      <div id='delegations-list-report-content' className='report-content'>
        <header className='text-center mb-8 pb-4 border-b-2 border-gray-300'>
          <h1 className='text-3xl font-bold text-gray-800'>
            Delegation List Report
          </h1>
          <p className='text-md text-gray-600'>
            {searchTerm || selectedStatus !== 'all' ? 
              'Filtered Delegations' : 
              'All Delegations'}
          </p>
          
          {/* Add Summary Statistics */}
          <DelegationSummary delegations={filteredDelegations} />
        </header>

        {/* Filter Section */}
        <div className='mb-6 no-print'>
          <Card className='shadow-md'>
            <CardContent className='p-4'>
              <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
                {/* Status Filter */}
                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>
                    Status
                  </label>
                  <Select
                    value={selectedStatus}
                    onValueChange={setSelectedStatus}>
                    <SelectTrigger className='w-full'>
                      <SelectValue placeholder='All Statuses' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='all'>All Statuses</SelectItem>
                      {STATUS_OPTIONS.map((status) => (
                        <SelectItem key={status} value={status}>
                          {formatDelegationStatusForDisplay(status)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Search Input */}
                <div className='relative'>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>
                    Search
                  </label>
                  <div className="relative">
                    <Input
                      type='text'
                      placeholder='Search delegations...'
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className='pl-10 pr-10'
                    />
                    <Search className='absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400' />
                    {searchTerm && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className='absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7'
                        onClick={() => setSearchTerm('')}
                      >
                        <X className='h-4 w-4' />
                        <span className="sr-only">Clear search</span>
                      </Button>
                    )}
                  </div>
                </div>
                
                {/* Date Range Filter - Placeholder for future implementation */}
                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>
                    Date Range
                  </label>
                  <Input
                    type='text'
                    placeholder='Date range filter coming soon'
                    disabled
                    className='opacity-50'
                  />
                </div>
              </div>
              
              {/* Filter Summary & Reset */}
              {(searchTerm || selectedStatus !== 'all') && (
                <div className='mt-4 flex items-center justify-between bg-gray-50 p-2 rounded'>
                  <div className='text-sm'>
                    <span className='font-medium'>Active Filters:</span>
                    {searchTerm && <span className='ml-2'>Search: "{searchTerm}"</span>}
                    {selectedStatus !== 'all' && (
                      <span className='ml-2'>
                        Status: {formatDelegationStatusForDisplay(selectedStatus)}
                      </span>
                    )}
                  </div>
                  <Button variant='ghost' size='sm' onClick={resetFilters}>
                    Reset Filters
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Empty State */}
        {filteredDelegations.length === 0 ? (
          <div className='text-center py-10 bg-gray-50 rounded-md'>
            <p className='text-gray-500'>No delegations match the current filter criteria.</p>
            <Button variant='outline' size='sm' onClick={resetFilters} className='mt-2'>
              Reset Filters
            </Button>
          </div>
        ) : (
          <>
            {/* Enhanced Table with Sorting */}
            <Card className='card-print'>
              <CardContent className='p-0'>
                <Table id='delegations-table'>
                  <TableHeader>
                    <TableRow>
                      <TableHead 
                        onClick={() => handleSort('eventName')} 
                        className='cursor-pointer'
                      >
                        Event Name {sortField === 'eventName' && (
                          <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                        )}
                      </TableHead>
                      <TableHead 
                        onClick={() => handleSort('location')} 
                        className='cursor-pointer'
                      >
                        Location {sortField === 'location' && (
                          <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                        )}
                      </TableHead>
                      <TableHead 
                        onClick={() => handleSort('durationFrom')} 
                        className='cursor-pointer'
                      >
                        Duration {sortField === 'durationFrom' && (
                          <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                        )}
                      </TableHead>
                      <TableHead 
                        onClick={() => handleSort('status')} 
                        className='cursor-pointer'
                      >
                        Status {sortField === 'status' && (
                          <span>{sortDirection === 'asc' ? '↑' : '↓'}</span>
                        )}
                      </TableHead>
                      <TableHead>Delegates</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedDelegations.map((delegation) => (
                      <TableRow key={delegation.id}>
                        <TableCell
                          className='font-medium max-w-xs print-text-wrap'
                          title={delegation.eventName}>
                          {delegation.eventName}
                        </TableCell>
                        <TableCell
                          className='max-w-[150px] print-text-wrap'
                          title={delegation.location}>
                          {delegation.location}
                        </TableCell>
                        <TableCell className='whitespace-nowrap'>
                          {formatDate(delegation.durationFrom)} -{' '}
                          {formatDate(delegation.durationTo)}
                        </TableCell>
                        <TableCell>
                          <Badge
                            className={cn(
                              'text-xs py-0.5 px-1.5 whitespace-nowrap',
                              getStatusColor(delegation.status)
                            )}>
                            {formatDelegationStatusForDisplay(delegation.status)}
                          </Badge>
                        </TableCell>
                        <TableCell
                          className='max-w-xs print-text-wrap'
                          title={delegation.delegates
                            .map((d) => d.name)
                            .join(', ')}>
                          {delegation.delegates.map((d) => d.name).join(', ') ||
                            '-'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
            
            {/* Pagination */}
            {filteredDelegations.length > itemsPerPage && (
              <div className='flex justify-center mt-4 no-print'>
                <PaginationControls
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </>
        )}

        <footer className='mt-10 pt-4 border-t-2 border-gray-300 text-center text-xs text-gray-500'>
          <p>Report generated on: {new Date().toLocaleDateString()}</p>
          <p>WorkHub - Delegation Management</p>
        </footer>
      </div>
      <style jsx global>{`
        .print-text-wrap {
          word-break: break-word;
          white-space: normal !important;
        }
      `}</style>
    </div>
  );
}

export default function DelegationListReportPage() {
  return (
    <Suspense
      fallback={<div className='text-center py-10'>Loading report...</div>}>
      <DelegationListReportContent />
    </Suspense>
  );
}
```

### Phase 3: Mobile Responsiveness Enhancements

Add the following styles to ensure proper mobile responsiveness:

```css
/* Add to globals.css or as inline styles */
@media (max-width: 640px) {
  .delegations-table-container {
    overflow-x: auto;
  }
  
  .filter-grid {
    grid-template-columns: 1fr;
  }
  
  .summary-grid {
    grid-template-columns: 1fr 1fr;
  }
}
```

### Phase 4: Accessibility Improvements

1. **Add ARIA labels to interactive elements**

```tsx
// Example for sort headers
<TableHead 
  onClick={() => handleSort('eventName')} 
  className='cursor-pointer'
  aria-sort={sortField === 'eventName' ? sortDirection : undefined}
  role="columnheader"
>
  Event Name {sortField === 'eventName' && (
    <span aria-hidden="true">{sortDirection === 'asc' ? '↑' : '↓'}</span>
  )}
</TableHead>

// Example for filter controls
<label htmlFor="status-filter" className='block text-sm font-medium text-gray-700 mb-1'>
  Status
</label>
<Select
  id="status-filter"
  value={selectedStatus}
  onValueChange={setSelectedStatus}
  aria-label="Filter by status"
>
  {/* ... */}
</Select>
```

2. **Ensure keyboard navigation**

```tsx
// Example for sort headers
<TableHead 
  onClick={() => handleSort('eventName')} 
  onKeyDown={(e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleSort('eventName');
    }
  }}
  tabIndex={0}
  role="button"
  aria-label="Sort by event name"
  className='cursor-pointer'
>
  {/* ... */}
</TableHead>
```

## Implementation Steps

1. Create the new components:
   - `DelegationSummary.tsx`
   - `PaginationControls.tsx` (if not already available)

2. Update the Delegations List Report page with the enhanced layout and functionality:
   - Add filter section with status filter and improved search
   - Add summary statistics
   - Add sorting functionality
   - Add pagination

3. Test thoroughly on different screen sizes and with assistive technologies

4. Refine based on testing feedback

## Future Enhancements

1. **Date Range Filter**:
   - Implement a proper date range picker component
   - Add date filtering functionality

2. **Advanced Filtering**:
   - Add more filter options (e.g., by delegate, by location)
   - Save filter preferences

3. **Export Customization**:
   - Allow users to select which columns to include in exports
   - Add more export formats