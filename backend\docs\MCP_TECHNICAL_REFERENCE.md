# Supabase MCP Technical Reference

## 📋 Overview

This document provides a technical reference for using **Supabase Model Context Protocol (MCP)** for database operations, debugging, and system maintenance. Based on the successful Hybrid RBAC implementation case study.

## 🔧 MCP Architecture

### **What is MCP?**
Model Context Protocol (MCP) is a standardized interface that enables AI assistants to interact directly with external services. For Supabase, this means:

- **Direct Database Access**: Execute SQL queries without manual intervention
- **Real-time Feedback**: Immediate results and error reporting
- **Automated Workflows**: Chain multiple operations together
- **Live Debugging**: Interactive problem-solving capabilities

### **MCP vs Traditional Database Management**

| **Aspect** | **Traditional** | **MCP** |
|------------|-----------------|---------|
| **Execution** | Manual copy/paste | Direct execution |
| **Feedback Loop** | Minutes to hours | Seconds |
| **Error Handling** | Manual debugging | Automated diagnosis |
| **Iteration Speed** | Slow (human-dependent) | Fast (AI-driven) |
| **Documentation** | Manual recording | Automatic logging |

## 🛠️ Core MCP Functions

### **1. execute_sql_supabase**

**Purpose**: Execute SQL queries directly against Supabase database

**Syntax**:
```typescript
execute_sql_supabase({
  project_id: "your-project-id",
  query: "SQL_QUERY_HERE"
})
```

**Common Use Cases**:
- Creating/modifying database functions
- Testing queries with real data
- Granting permissions
- Schema modifications
- Data validation

**Example**:
```typescript
execute_sql_supabase({
  project_id: "abylqjnpaegeqwktcukn",
  query: `
    CREATE OR REPLACE FUNCTION public.test_function()
    RETURNS TEXT
    LANGUAGE plpgsql
    AS $$
    BEGIN
      RETURN 'Hello from MCP!';
    END;
    $$;
  `
})
```

### **2. Database Inspection Commands**

#### **Check Table Structure**
```sql
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'your_table' 
AND table_schema = 'public';
```

#### **List All Functions**
```sql
SELECT routine_name, routine_type, data_type
FROM information_schema.routines 
WHERE routine_schema = 'public';
```

#### **Check Function Parameters**
```sql
SELECT parameter_name, data_type, parameter_mode
FROM information_schema.parameters 
WHERE specific_name = 'your_function_name';
```

#### **Verify Permissions**
```sql
SELECT grantee, privilege_type, is_grantable
FROM information_schema.routine_privileges 
WHERE routine_name = 'your_function';
```

## 🔍 MCP Debugging Patterns

### **Pattern 1: Systematic Function Testing**

```typescript
// Step 1: Test function exists
execute_sql_supabase({
  project_id: "project-id",
  query: "SELECT public.your_function('test_param');"
})

// Step 2: Check function definition if error
execute_sql_supabase({
  project_id: "project-id", 
  query: `
    SELECT routine_definition 
    FROM information_schema.routines 
    WHERE routine_name = 'your_function';
  `
})

// Step 3: Verify parameter types
execute_sql_supabase({
  project_id: "project-id",
  query: `
    SELECT parameter_name, data_type 
    FROM information_schema.parameters 
    WHERE specific_name = 'your_function';
  `
})
```

### **Pattern 2: Data Type Validation**

```typescript
// Check actual data types in table
execute_sql_supabase({
  project_id: "project-id",
  query: `
    SELECT column_name, data_type, character_maximum_length
    FROM information_schema.columns 
    WHERE table_name = 'your_table';
  `
})

// Test with actual data
execute_sql_supabase({
  project_id: "project-id",
  query: "SELECT id, pg_typeof(id) FROM your_table LIMIT 1;"
})
```

### **Pattern 3: Permission Debugging**

```typescript
// Check current permissions
execute_sql_supabase({
  project_id: "project-id",
  query: `
    SELECT grantee, privilege_type 
    FROM information_schema.routine_privileges 
    WHERE routine_name = 'your_function';
  `
})

// Grant missing permissions
execute_sql_supabase({
  project_id: "project-id",
  query: "GRANT EXECUTE ON FUNCTION public.your_function(param_type) TO role_name;"
})

// Verify permissions granted
execute_sql_supabase({
  project_id: "project-id",
  query: `
    SELECT grantee, privilege_type 
    FROM information_schema.routine_privileges 
    WHERE routine_name = 'your_function';
  `
})
```

## 🚀 Advanced MCP Techniques

### **1. Debug Function Creation**

Create temporary debug versions of functions to trace execution:

```sql
CREATE OR REPLACE FUNCTION public.debug_your_function(param_type)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    debug_info JSONB := '{}';
    result_var your_return_type;
BEGIN
    -- Log input parameters
    debug_info := jsonb_set(debug_info, '{input}', to_jsonb(param_name));
    
    -- Your function logic here with debug logging
    -- debug_info := jsonb_set(debug_info, '{step1}', 'completed');
    
    -- Log final result
    debug_info := jsonb_set(debug_info, '{result}', to_jsonb(result_var));
    
    RETURN debug_info;
EXCEPTION WHEN OTHERS THEN
    debug_info := jsonb_set(debug_info, '{error}', to_jsonb(SQLERRM));
    RETURN debug_info;
END;
$$;
```

### **2. Batch Operations**

Execute multiple related operations in sequence:

```typescript
// Operation 1: Create function
execute_sql_supabase({
  project_id: "project-id",
  query: "CREATE OR REPLACE FUNCTION..."
})

// Operation 2: Grant permissions  
execute_sql_supabase({
  project_id: "project-id",
  query: "GRANT EXECUTE ON FUNCTION..."
})

// Operation 3: Test function
execute_sql_supabase({
  project_id: "project-id",
  query: "SELECT your_function('test');"
})
```

### **3. Schema Validation**

Verify database schema matches expectations:

```sql
-- Check if table exists
SELECT EXISTS (
  SELECT FROM information_schema.tables 
  WHERE table_schema = 'public' 
  AND table_name = 'expected_table'
);

-- Verify required columns exist
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'your_table' 
AND column_name IN ('col1', 'col2', 'col3');

-- Check constraints
SELECT constraint_name, constraint_type 
FROM information_schema.table_constraints 
WHERE table_name = 'your_table';
```

## 📊 MCP Best Practices

### **1. Error Handling**
- Always test functions with real data
- Use debug versions for complex functions
- Check error messages for data type mismatches
- Verify permissions before and after grants

### **2. Data Type Management**
- Always verify actual column data types
- Don't assume UUID when it might be TEXT
- Use `pg_typeof()` to check runtime types
- Cast parameters explicitly when needed

### **3. Permission Management**
- Grant permissions immediately after creating functions
- Test permissions with actual user roles
- Use information_schema to verify grants
- Document permission requirements

### **4. Testing Strategy**
- Test with minimal data first
- Use actual user IDs and data
- Verify both success and failure cases
- Clean up test data and debug functions

## 🔧 Common MCP Troubleshooting

### **Issue: Function Not Found**
```sql
-- Check if function exists
SELECT routine_name FROM information_schema.routines 
WHERE routine_name = 'your_function';

-- Check function signature
SELECT routine_name, data_type, routine_definition
FROM information_schema.routines 
WHERE routine_name = 'your_function';
```

### **Issue: Permission Denied**
```sql
-- Check current permissions
SELECT grantee, privilege_type FROM information_schema.routine_privileges 
WHERE routine_name = 'your_function';

-- Grant required permissions
GRANT EXECUTE ON FUNCTION public.your_function(param_types) TO role_name;
```

### **Issue: Data Type Mismatch**
```sql
-- Check parameter types
SELECT parameter_name, data_type FROM information_schema.parameters 
WHERE specific_name = 'your_function';

-- Check actual column types
SELECT column_name, data_type FROM information_schema.columns 
WHERE table_name = 'your_table';
```

### **Issue: Function Returns Unexpected Results**
```sql
-- Create debug version
CREATE OR REPLACE FUNCTION debug_your_function(...)
RETURNS JSONB
-- Include detailed logging and error handling
```

## 📚 MCP Integration Examples

### **Example 1: Auth Hook Debugging**
```typescript
// Test auth hook with real user
execute_sql_supabase({
  project_id: "project-id",
  query: `
    SELECT public.custom_access_token_hook(
      '{"user_id": "real-user-id", "claims": {"sub": "real-user-id"}}'::jsonb
    );
  `
})
```

### **Example 2: Role Function Testing**
```typescript
// Test role checking function
execute_sql_supabase({
  project_id: "project-id",
  query: "SELECT public.get_user_role('test-user-id');"
})
```

### **Example 3: Permission Verification**
```typescript
// Check and grant permissions
execute_sql_supabase({
  project_id: "project-id",
  query: `
    SELECT grantee, privilege_type 
    FROM information_schema.routine_privileges 
    WHERE routine_name = 'get_user_role';
  `
})
```

## 🎯 Success Metrics

### **MCP Effectiveness Indicators**
- **Resolution Time**: < 30 minutes for complex issues
- **Iteration Speed**: < 1 minute per test cycle
- **Error Rate**: < 5% due to automated validation
- **Documentation**: 100% of changes automatically logged

### **Quality Assurance**
- All functions tested with real data
- Permissions verified before deployment
- Error handling validated
- Performance impact assessed

---

**Document Version**: 1.0  
**Last Updated**: January 24, 2025  
**Related**: SUPABASE_MCP_INTEGRATION_GUIDE.md
