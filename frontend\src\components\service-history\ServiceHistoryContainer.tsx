'use client';

/**
 * @deprecated This component is deprecated and will be removed in a future version.
 * Please use EnhancedServiceHistoryContainer instead, which provides improved
 * functionality including sorting, pagination, and summary statistics.
 *
 * Migration guide:
 * 1. Import EnhancedServiceHistoryContainer instead of ServiceHistoryContainer
 * 2. Fetch service records in your component and pass them as the 'records' prop
 * 3. Handle loading state and errors in your component
 *
 * Example:
 * ```tsx
 * import { EnhancedServiceHistoryContainer } from '@/components/service-history/EnhancedServiceHistoryContainer';
 *
 * // In your component:
 * const [records, setRecords] = useState<EnrichedServiceRecord[]>([]);
 * const [isLoading, setIsLoading] = useState(true);
 * const [error, setError] = useState<string | null>(null);
 *
 * // Fetch records...
 *
 * return (
 *   <EnhancedServiceHistoryContainer
 *     records={records}
 *     isLoading={isLoading}
 *     error={error}
 *     onRetry={handleRetry}
 *     showVehicleInfo={true}
 *     vehicleSpecific={false}
 *   />
 * );
 * ```
 */

import {useEffect, useState, useMemo, useCallback} from 'react';
import {
	getAllEnrichedServiceRecords,
	getAllServiceRecords,
} from '@/lib/services/serviceRecordService';
import {getVehicles} from '@/lib/services/vehicleService';
import type {EnrichedServiceRecord, Vehicle, ServiceRecord} from '@/lib/types';
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from '@/components/ui/table';
import {Card, CardContent} from '@/components/ui/card';
import {Alert, AlertDescription, AlertTitle} from '@/components/ui/alert';
import {Button} from '@/components/ui/button';
import {Skeleton} from '@/components/ui/skeleton';
import {AlertTriangle, Info} from 'lucide-react';
import {ApiError, ApiErrorType} from '@/lib/types/api';

// Common service types for filtering
const commonServicesList = [
	'Oil Change',
	'Tire Rotation',
	'Brake Service',
	'Battery Replacement',
	'Air Filter Replacement',
	'Cabin Air Filter Replacement',
	'Wiper Blade Replacement',
	'Fluid Check/Top-up',
	'Spark Plug Replacement',
	'Coolant Flush',
	'Transmission Service',
	'Wheel Alignment',
	'State Inspection',
	'Other',
];

interface ServiceHistoryContainerProps {
	vehicles?: Vehicle[];
	selectedVehicleId?: string;
	selectedServiceType?: string;
	searchTerm?: string;
}

export default function ServiceHistoryContainer({
	vehicles = [],
	selectedVehicleId = 'all',
	selectedServiceType = 'all',
	searchTerm = '',
}: ServiceHistoryContainerProps) {
	const [enrichedServiceRecords, setEnrichedServiceRecords] = useState<
		EnrichedServiceRecord[]
	>([]);
	const [filteredRecords, setFilteredRecords] = useState<
		EnrichedServiceRecord[]
	>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [retryCount, setRetryCount] = useState(0);

	// Display deprecation warning
	useEffect(() => {
		console.warn(
			'DEPRECATION WARNING: ServiceHistoryContainer is deprecated and will be removed in a future version. ' +
				'Please migrate to EnhancedServiceHistoryContainer for improved functionality.'
		);
	}, []);

	// Fetch service records with improved error handling and loading states
	const fetchServiceRecords = useCallback(async () => {
		// Don't check isLoading here to allow retries even if we're in a loading state
		// This prevents the component from getting stuck in a loading state

		setIsLoading(true);
		setError(null);

		try {
			console.info('Fetching enriched service records');

			// Add a timeout to prevent hanging indefinitely
			const fetchWithTimeout = async () => {
				const timeout = 15000; // 15 seconds timeout
				const controller = new AbortController();
				const timeoutId = setTimeout(() => controller.abort(), timeout);

				try {
					// Use the service function with options that include the signal
					const records = await getAllEnrichedServiceRecords({
						signal: controller.signal,
						skipRetryLogging: true, // Avoid console spam in tests
					});
					clearTimeout(timeoutId);
					return records;
				} catch (error) {
					clearTimeout(timeoutId);
					throw error;
				}
			};

			try {
				const records = await fetchWithTimeout();

				// Check if records is an array before accessing length
				if (Array.isArray(records)) {
					console.debug('Enriched service records fetched successfully', {
						count: records.length,
					});

					setEnrichedServiceRecords(records);
				} else {
					console.error(
						'Received non-array response for service records:',
						records
					);
					setError(
						'Received invalid data format from server. Please try again.'
					);
				}
			} catch (error) {
				// If the enriched endpoint fails with a 404, fall back to the regular endpoint
				// and manually enrich the data
				if (error instanceof ApiError && error.status === 404) {
					console.warn(
						'Enriched endpoint not found, falling back to regular endpoints'
					);

					try {
						// Fetch service records and vehicles in parallel
						const [serviceRecords, vehicles] = await Promise.all([
							getAllServiceRecords(),
							getVehicles(),
						]);

						// Create a map of vehicles for quick lookup
						const vehicleMap = new Map(
							vehicles.map((vehicle) => [vehicle.id, vehicle])
						);

						// Manually enrich the service records
						const enrichedRecords: EnrichedServiceRecord[] = serviceRecords.map(
							(record: ServiceRecord) => {
								const vehicle = vehicleMap.get(record.vehicleId);

								return {
									...record,
									vehicleId: record.vehicleId.toString(),
									vehicleMake: vehicle?.make || 'Unknown',
									vehicleModel: vehicle?.model || 'Unknown',
									vehicleYear: vehicle?.year || 0,
									vehiclePlateNumber: vehicle?.licensePlate || 'Unknown',
								};
							}
						);

						console.debug('Manually enriched service records:', {
							count: enrichedRecords.length,
						});

						setEnrichedServiceRecords(enrichedRecords);
						return; // Exit early since we've handled the error
					} catch (fallbackError) {
						console.error(
							'Fallback to regular endpoints failed:',
							fallbackError
						);
						// Continue to the main error handler
						throw fallbackError;
					}
				}

				// If it's not a 404 or the fallback failed, rethrow the error
				throw error;
			}
		} catch (error) {
			console.error('Failed to fetch service records:', error);

			// Provide a more specific error message based on error type
			if (error instanceof ApiError) {
				// Use the enhanced error handling from our ApiError class
				switch (error.errorType) {
					case ApiErrorType.TIMEOUT:
						setError('Request timed out. Please try again later.');
						break;
					case ApiErrorType.NETWORK_ERROR:
						setError(
							'Network error. Please check your connection and try again.'
						);
						break;
					case ApiErrorType.VALIDATION_ERROR:
						setError(
							'Invalid request parameters. Please check your filters and try again.'
						);
						break;
					case ApiErrorType.AUTHENTICATION_ERROR:
						setError('Authentication required. Please log in and try again.');
						break;
					case ApiErrorType.AUTHORIZATION_ERROR:
						setError(
							'You do not have permission to access these service records.'
						);
						break;
					case ApiErrorType.SERVER_ERROR:
						setError(
							'The server encountered an error. Please try again later.'
						);
						break;
					case ApiErrorType.RATE_LIMIT:
						setError('Too many requests. Please try again later.');
						break;
					case ApiErrorType.NOT_FOUND:
						setError(
							'The service records endpoint could not be found. Please check the server configuration.'
						);
						break;
					case ApiErrorType.PARSING_ERROR:
						setError(
							'Could not parse the server response. Please try again or contact support.'
						);
						break;
					default:
						setError(
							error.getFormattedMessage() ||
								'Failed to load service records. Please try again later.'
						);
				}
			} else if (error instanceof Error) {
				// Handle other error types
				if (error.name === 'AbortError') {
					setError('Request timed out. Please try again later.');
				} else if (
					error.message.includes('timeout') ||
					error.message.includes('network')
				) {
					setError(
						'Network error. Please check your connection and try again.'
					);
				} else {
					setError('Failed to load service records. Please try again later.');
				}
			} else {
				setError('Failed to load service records. Please try again later.');
			}

			// Log detailed error information for debugging
			if (process.env.NODE_ENV !== 'production') {
				console.debug('Detailed error information:', {
					error,
					type: error instanceof ApiError ? error.errorType : 'Unknown',
					message: error instanceof Error ? error.message : String(error),
					stack: error instanceof Error ? error.stack : undefined,
				});
			}
		} finally {
			setIsLoading(false);
		}
	}, []); // Remove isLoading from dependencies to prevent infinite loops

	// Initial fetch
	useEffect(() => {
		fetchServiceRecords();
	}, [fetchServiceRecords, retryCount]);

	// Filter records based on selected filters
	useEffect(() => {
		let tempRecords = [...enrichedServiceRecords];

		// Filter by vehicle
		if (selectedVehicleId !== 'all') {
			tempRecords = tempRecords.filter(
				(record) => record.vehicleId === selectedVehicleId
			);
		}

		// Filter by service type
		if (selectedServiceType !== 'all') {
			tempRecords = tempRecords.filter((record) =>
				record.servicePerformed.includes(selectedServiceType)
			);
		}

		// Filter by search term
		if (searchTerm) {
			const lowerSearchTerm = searchTerm.toLowerCase();
			tempRecords = tempRecords.filter(
				(record) =>
					`${record.vehicleMake} ${record.vehicleModel}`
						.toLowerCase()
						.includes(lowerSearchTerm) ||
					record.servicePerformed
						.join(' ')
						.toLowerCase()
						.includes(lowerSearchTerm) ||
					(record.notes &&
						record.notes.toLowerCase().includes(lowerSearchTerm)) ||
					(record.vehiclePlateNumber &&
						record.vehiclePlateNumber
							.toLowerCase()
							.includes(lowerSearchTerm)) ||
					record.odometer.toString().includes(lowerSearchTerm)
			);
		}

		setFilteredRecords(tempRecords);
	}, [
		enrichedServiceRecords,
		selectedVehicleId,
		selectedServiceType,
		searchTerm,
	]);

	// Get unique service types from records
	const uniqueServiceTypes = useMemo(() => {
		const types = new Set<string>();
		enrichedServiceRecords.forEach((record) => {
			record.servicePerformed.forEach((service) => types.add(service));
		});
		// Ensure common services are listed even if not used yet for a complete filter
		commonServicesList.forEach((service) => types.add(service));
		return Array.from(types).sort();
	}, [enrichedServiceRecords]);

	// Handle retry
	const handleRetry = useCallback(() => {
		setRetryCount((prev) => prev + 1);
	}, []);

	// Render loading state
	if (isLoading) {
		return (
			<div className='space-y-4' data-testid='loading-skeleton'>
				{[...Array(5)].map((_, i) => (
					<div key={i} className='flex items-center space-x-4 p-4 border-b'>
						<Skeleton className='h-6 w-1/6' />
						<Skeleton className='h-6 w-1/6' />
						<Skeleton className='h-6 w-2/6' />
						<Skeleton className='h-6 w-1/6' />
						<Skeleton className='h-6 w-1/6' />
					</div>
				))}
			</div>
		);
	}

	// Render error state
	if (error) {
		return (
			<Alert variant='destructive' className='mb-6'>
				<AlertTriangle className='h-4 w-4' />
				<AlertTitle>Error</AlertTitle>
				<AlertDescription>{error}</AlertDescription>
				<Button
					variant='outline'
					size='sm'
					onClick={handleRetry}
					className='mt-2'>
					Try Again
				</Button>
			</Alert>
		);
	}

	// Render empty state
	if (filteredRecords.length === 0) {
		return (
			<Alert
				variant={
					searchTerm ||
					selectedVehicleId !== 'all' ||
					selectedServiceType !== 'all'
						? 'default'
						: 'destructive'
				}
				className='mb-6'>
				<Info className='h-4 w-4' />
				<AlertTitle>
					{searchTerm ||
					selectedVehicleId !== 'all' ||
					selectedServiceType !== 'all'
						? 'No Matching Records'
						: 'No Service Records Yet'}
				</AlertTitle>
				<AlertDescription>
					{searchTerm ||
					selectedVehicleId !== 'all' ||
					selectedServiceType !== 'all'
						? 'No service records match your current filters. Try adjusting your search or filter criteria.'
						: "There are no service records logged for any vehicle. You can add records via the 'My Vehicles' page."}
				</AlertDescription>
			</Alert>
		);
	}

	// Render service records
	return (
		<Card className='shadow-md card-print'>
			<CardContent className='p-0'>
				<div className='overflow-x-auto'>
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>Date</TableHead>
								<TableHead>Vehicle</TableHead>
								<TableHead>Service(s)</TableHead>
								<TableHead>Odometer</TableHead>
								<TableHead className='text-right'>Cost</TableHead>
								<TableHead className='print-notes-col'>Notes</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{filteredRecords.map((record) => (
								<TableRow key={record.id}>
									<TableCell>
										{new Date(record.date).toLocaleDateString()}
									</TableCell>
									<TableCell>
										{record.vehicleMake} {record.vehicleModel} (
										{record.vehicleYear})
										{record.vehiclePlateNumber && (
											<span className='block text-xs text-muted-foreground'>
												{record.vehiclePlateNumber}
											</span>
										)}
									</TableCell>
									<TableCell
										className='max-w-xs truncate print-service-col'
										title={record.servicePerformed.join(', ')}>
										{record.servicePerformed.join(', ')}
									</TableCell>
									<TableCell>{record.odometer.toLocaleString()}</TableCell>
									<TableCell className='text-right'>
										{record.cost ? `$${Number(record.cost).toFixed(2)}` : '-'}
									</TableCell>
									<TableCell
										className='max-w-xs truncate print-notes-col'
										title={record.notes}>
										{record.notes || '-'}
									</TableCell>
								</TableRow>
							))}
						</TableBody>
					</Table>
				</div>
			</CardContent>
		</Card>
	);
}
