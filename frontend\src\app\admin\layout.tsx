import type {Metadata} from 'next';
import {
	Bread<PERSON>rumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import {Settings} from 'lucide-react';

export const metadata: Metadata = {
	title: 'Admin Dashboard - WorkHub',
	description: 'Administrative dashboard for WorkHub system',
};

export default function AdminLayout({children}: {children: React.ReactNode}) {
	return (
		<div className='space-y-6'>
			<div className='flex items-center'>
				<Breadcrumb className='mb-4'>
					<BreadcrumbList>
						<BreadcrumbItem>
							<BreadcrumbLink href='/'>Home</BreadcrumbLink>
						</BreadcrumbItem>
						<BreadcrumbSeparator />
						<BreadcrumbItem>
							<BreadcrumbPage>Admin</BreadcrumbPage>
						</BreadcrumbItem>
					</BreadcrumbList>
				</Breadcrumb>
			</div>
			<div className='flex-1'>{children}</div>
		</div>
	);
}
