'use client';

import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface SkeletonLoaderProps {
  className?: string;
  count?: number;
  height?: number | string;
  width?: number | string;
  circle?: boolean;
  variant?: 'default' | 'card' | 'table' | 'list';
  testId?: string;
}

/**
 * Skeleton loader component for content placeholders
 * Supports different variants for common UI patterns
 */
const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  className,
  count = 1,
  height = 20,
  width = '100%',
  circle = false,
  variant = 'default',
  testId = 'loading-skeleton',
}) => {
  // Convert height/width to string with px if they're numbers
  const heightStyle = typeof height === 'number' ? `${height}px` : height;
  const widthStyle = typeof width === 'number' ? `${width}px` : width;

  // Generate skeleton items based on count
  const renderDefaultSkeletons = () => {
    return Array(count)
      .fill(0)
      .map((_, index) => (
        <Skeleton
          key={index}
          className={cn(
            circle ? 'rounded-full' : 'rounded-md',
            className
          )}
          style={{ height: heightStyle, width: widthStyle }}
        />
      ));
  };

  // Render card skeleton with header, content, and footer
  const renderCardSkeleton = () => (
    <div className="space-y-3" data-testid={testId}>
      <Skeleton className="h-8 w-3/4 rounded-md" />
      <Skeleton className="h-32 w-full rounded-md" />
      <div className="flex justify-between">
        <Skeleton className="h-6 w-24 rounded-md" />
        <Skeleton className="h-6 w-16 rounded-md" />
      </div>
    </div>
  );

  // Render table skeleton with header and rows
  const renderTableSkeleton = () => (
    <div className="space-y-3" data-testid={testId}>
      <div className="flex gap-4">
        {Array(3)
          .fill(0)
          .map((_, index) => (
            <Skeleton key={index} className="h-8 flex-1 rounded-md" />
          ))}
      </div>
      {Array(count)
        .fill(0)
        .map((_, index) => (
          <div key={index} className="flex gap-4">
            {Array(3)
              .fill(0)
              .map((_, cellIndex) => (
                <Skeleton key={cellIndex} className="h-6 flex-1 rounded-md" />
              ))}
          </div>
        ))}
    </div>
  );

  // Render list skeleton with items
  const renderListSkeleton = () => (
    <div className="space-y-3" data-testid={testId}>
      {Array(count)
        .fill(0)
        .map((_, index) => (
          <div key={index} className="flex items-center gap-4">
            <Skeleton className="h-12 w-12 rounded-full" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-1/3 rounded-md" />
              <Skeleton className="h-4 w-full rounded-md" />
            </div>
          </div>
        ))}
    </div>
  );

  // Render the appropriate skeleton based on variant
  switch (variant) {
    case 'card':
      return renderCardSkeleton();
    case 'table':
      return renderTableSkeleton();
    case 'list':
      return renderListSkeleton();
    default:
      return (
        <div className="space-y-2" data-testid={testId}>
          {renderDefaultSkeletons()}
        </div>
      );
  }
};

export default SkeletonLoader;
