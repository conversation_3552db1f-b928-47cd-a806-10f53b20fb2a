/**
 * Test Script: Hybrid RBAC Auth Hook Verification
 * 
 * This script tests the custom access token hook to ensure JWT claims
 * are being properly injected with user roles from the user_profiles table.
 */

import { createClient } from '@supabase/supabase-js';
import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
    console.error('❌ Missing Supabase environment variables');
    console.error('Required: SUPABASE_URL, SUPABASE_ANON_KEY');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Test the auth hook by signing in a user and checking JWT claims
 */
async function testAuthHook() {
    console.log('🔐 Testing Hybrid RBAC Auth Hook Implementation\n');

    try {
        // Test 1: Check if we can decode JWT and see custom claims
        console.log('📋 Test 1: JWT Custom Claims Verification');
        console.log('==========================================');

        // Get current session (if any)
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
            console.error('❌ Error getting session:', sessionError.message);
            return;
        }

        if (!session) {
            console.log('ℹ️  No active session found.');
            console.log('💡 To test the auth hook:');
            console.log('   1. Sign in through your frontend application');
            console.log('   2. Run this script again');
            console.log('   3. Or provide test credentials below\n');
            
            // Optionally prompt for test credentials
            await promptForTestCredentials();
            return;
        }

        console.log('✅ Active session found');
        console.log(`👤 User ID: ${session.user.id}`);
        console.log(`📧 Email: ${session.user.email}\n`);

        // Decode the JWT to check custom claims
        const token = session.access_token;
        const decoded = jwt.decode(token, { complete: true });

        console.log('🔍 JWT Token Analysis:');
        console.log('=====================');
        
        if (decoded && decoded.payload) {
            const payload = decoded.payload;
            
            // Check for custom claims
            if (payload.custom_claims) {
                console.log('✅ Custom claims found in JWT:');
                console.log(`   - User Role: ${payload.custom_claims.user_role || 'Not set'}`);
                console.log(`   - Is Active: ${payload.custom_claims.is_active || 'Not set'}`);
                console.log(`   - Employee ID: ${payload.custom_claims.employee_id || 'Not linked'}`);
                
                // Verify against database
                await verifyClaimsAgainstDatabase(session.user.id, payload.custom_claims);
            } else {
                console.log('❌ No custom claims found in JWT');
                console.log('🔧 This indicates the auth hook may not be working properly');
                
                // Check if user profile exists
                await checkUserProfile(session.user.id);
            }
        } else {
            console.log('❌ Could not decode JWT token');
        }

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

/**
 * Verify JWT claims against the database
 */
async function verifyClaimsAgainstDatabase(userId, claims) {
    console.log('\n🔍 Database Verification:');
    console.log('========================');

    try {
        // Query user profile from database
        const { data: profile, error } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('id', userId)
            .single();

        if (error) {
            console.log('❌ Error querying user profile:', error.message);
            return;
        }

        if (!profile) {
            console.log('❌ No user profile found in database');
            console.log('💡 The auth hook should create a default profile for new users');
            return;
        }

        console.log('✅ User profile found in database:');
        console.log(`   - Role: ${profile.role}`);
        console.log(`   - Is Active: ${profile.is_active}`);
        console.log(`   - Employee ID: ${profile.employee_id || 'Not linked'}`);

        // Compare claims with database
        const claimsMatch = 
            claims.user_role === profile.role &&
            claims.is_active === profile.is_active &&
            claims.employee_id === profile.employee_id;

        if (claimsMatch) {
            console.log('✅ JWT claims match database values - Auth hook working correctly!');
        } else {
            console.log('❌ JWT claims do not match database values');
            console.log('🔧 The auth hook may need to be refreshed or reconfigured');
        }

    } catch (error) {
        console.error('❌ Database verification failed:', error.message);
    }
}

/**
 * Check if user profile exists for debugging
 */
async function checkUserProfile(userId) {
    console.log('\n🔍 User Profile Check:');
    console.log('=====================');

    try {
        const { data: profile, error } = await supabase
            .from('user_profiles')
            .select('*')
            .eq('id', userId)
            .single();

        if (error) {
            console.log('❌ No user profile found');
            console.log('💡 You may need to create a user profile manually or check the trigger');
            return;
        }

        console.log('✅ User profile exists:', profile);
    } catch (error) {
        console.error('❌ Profile check failed:', error.message);
    }
}

/**
 * Prompt for test credentials (optional)
 */
async function promptForTestCredentials() {
    console.log('🔐 Test with credentials (optional):');
    console.log('===================================');
    console.log('You can test by signing in with test credentials');
    console.log('This would require implementing a sign-in flow here');
    console.log('For now, please sign in through your application and run this script again\n');
}

// Run the test
testAuthHook().catch(console.error);
