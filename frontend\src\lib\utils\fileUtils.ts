/**
 * File utility functions for working with files
 * Used primarily for CSV export functionality
 */
import <PERSON> from 'papaparse';

/**
 * Generic function to trigger a browser download for a given Blob and filename
 * @param blob The file content as a Blob
 * @param fileName The name of the file to download
 */
export const downloadFile = (blob: Blob, fileName: string): void => {
	const url = URL.createObjectURL(blob);
	const a = document.createElement('a');
	a.href = url;
	a.download = fileName;
	document.body.appendChild(a); // Required for Firefox
	a.click();
	document.body.removeChild(a);
	URL.revokeObjectURL(url); // Clean up
};

/**
 * Generate a CSV from data and trigger download
 * @param data - 2D array of data to export
 * @param headers - Array of column headers
 * @param fileName - Name of the CSV file to download
 */
export const generateCsvFromData = async (
	data: any[][],
	headers: string[],
	fileName: string
): Promise<void> => {
	if (!fileName.toLowerCase().endsWith('.csv')) {
		fileName += '.csv';
	}

	// Format data with headers
	const csvData = [headers, ...data];

	// Convert to CSV using PapaParse
	const csv = Papa.unparse(csvData);
	const blob = new Blob([csv], {type: 'text/csv;charset=utf-8;'});

	// Trigger download
	downloadFile(blob, fileName);
};

/**
 * Extract table data for CSV export
 * @param tableElementIdOrSelector - ID or selector of the HTML table to extract data from
 * @returns Object with headers and data arrays
 */
export const extractTableDataForCsv = (
	tableElementIdOrSelector: string
): {headers: string[]; data: any[][]} => {
	const table = document.querySelector<HTMLTableElement>(
		tableElementIdOrSelector
	);

	if (!table) {
		throw new Error(`Table element not found: ${tableElementIdOrSelector}`);
	}

	// Extract headers
	const headerRow = table.querySelector('thead tr');
	if (!headerRow) {
		throw new Error('Table header row not found');
	}

	const headers: string[] = [];
	headerRow.querySelectorAll('th').forEach((th) => {
		// Skip any action/control columns that have data-skip-export attribute
		if (!th.hasAttribute('data-skip-export')) {
			headers.push(th.textContent?.trim() || '');
		}
	});

	// Extract data rows
	const data: any[][] = [];
	table.querySelectorAll('tbody tr').forEach((tr) => {
		const rowData: any[] = [];
		let colIndex = 0;

		tr.querySelectorAll('td').forEach((td) => {
			// Skip any cells in columns that should be skipped (based on header)
			const headerTh = headerRow.querySelectorAll('th')[colIndex];
			colIndex++;

			if (headerTh && !headerTh.hasAttribute('data-skip-export')) {
				// Get text content, preferring data-export-value if it exists
				const cellValue =
					td.getAttribute('data-export-value') || td.textContent?.trim() || '';
				rowData.push(cellValue);
			}
		});

		data.push(rowData);
	});

	return {headers, data};
};
