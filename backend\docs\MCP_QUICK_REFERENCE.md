# Supabase MCP Quick Reference Card

## 🚀 Essential MCP Commands

### **Basic SQL Execution**
```typescript
execute_sql_supabase({
  project_id: "your-project-id",
  query: "YOUR_SQL_HERE"
})
```

### **Function Testing**
```sql
-- Test function exists and works
SELECT public.your_function('test_param');

-- Check function definition
SELECT routine_definition FROM information_schema.routines 
WHERE routine_name = 'your_function';
```

### **Data Type Checking**
```sql
-- Check column types
SELECT column_name, data_type FROM information_schema.columns 
WHERE table_name = 'your_table';

-- Check runtime type
SELECT pg_typeof(column_name) FROM your_table LIMIT 1;
```

### **Permission Management**
```sql
-- Check permissions
SELECT grantee, privilege_type FROM information_schema.routine_privileges 
WHERE routine_name = 'your_function';

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.your_function(param_type) TO role_name;
```

## 🔍 Common Debugging Patterns

### **Pattern 1: Function Not Working**
1. Test function exists: `SELECT public.function_name('test');`
2. Check parameters: `SELECT parameter_name, data_type FROM information_schema.parameters WHERE specific_name = 'function_name';`
3. Verify permissions: `SELECT grantee FROM information_schema.routine_privileges WHERE routine_name = 'function_name';`
4. Create debug version with logging

### **Pattern 2: Data Type Issues**
1. Check actual column types: `SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'table_name';`
2. Test with real data: `SELECT column, pg_typeof(column) FROM table LIMIT 1;`
3. Fix function parameter types
4. Re-test with corrected types

### **Pattern 3: Auth Hook Problems**
1. Test hook with real user: `SELECT public.custom_access_token_hook('{"user_id": "real-id", "claims": {}}'::jsonb);`
2. Check user profile exists: `SELECT * FROM user_profiles WHERE id = 'user-id';`
3. Verify claims structure in result
4. Test permissions for auth roles

## 🛠️ Quick Fixes

### **Create Debug Function**
```sql
CREATE OR REPLACE FUNCTION debug_function_name(param_type)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    debug_info JSONB := '{}';
BEGIN
    debug_info := jsonb_set(debug_info, '{input}', to_jsonb(param_name));
    -- Your logic here with debug logging
    RETURN debug_info;
EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object('error', SQLERRM);
END;
$$;
```

### **Fix Data Type Mismatch**
```sql
-- Change UUID to TEXT if needed
CREATE OR REPLACE FUNCTION your_function(user_id TEXT)  -- Changed from UUID
RETURNS your_return_type
-- Rest of function
```

### **Grant Standard Permissions**
```sql
-- For auth hooks
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(JSONB) TO supabase_auth_admin;
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(JSONB) TO service_role;

-- For helper functions  
GRANT EXECUTE ON FUNCTION public.helper_function(TEXT) TO authenticated;
```

## 📊 Success Indicators

### **✅ Working System**
- Functions return expected results
- No permission errors
- Custom claims appear in JWT
- All verification tests pass

### **❌ Common Issues**
- `function does not exist` → Check function name/parameters
- `permission denied` → Grant proper permissions
- `invalid input syntax for type uuid` → Change parameter to TEXT
- `column "is_active" is ambiguous` → Use table aliases

## 🎯 MCP Workflow

1. **Diagnose**: Use MCP to test and identify issues
2. **Debug**: Create debug versions with detailed logging  
3. **Fix**: Apply corrections via MCP
4. **Verify**: Test fixes immediately
5. **Clean**: Remove debug functions and duplicates

## 📚 Related Documents

- **SUPABASE_MCP_INTEGRATION_GUIDE.md**: Complete case study
- **MCP_TECHNICAL_REFERENCE.md**: Detailed technical reference
- **RBAC_IMPLEMENTATION_COMPLETE.md**: System overview

---

**Quick Reference Version**: 1.0  
**For**: Supabase MCP Integration  
**Project**: WorkHub Hybrid RBAC System
