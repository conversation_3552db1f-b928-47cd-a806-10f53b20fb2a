'use client';

import React from 'react';
import type {Delegation, DelegationStatus} from '@/lib/types';
import {Card, CardContent} from '@/components/ui/card';
import {cn} from '@/lib/utils';

// Status options for dynamic rendering
const STATUS_OPTIONS: DelegationStatus[] = [
	'Planned',
	'Confirmed',
	'In_Progress',
	'Completed',
	'Cancelled',
	'No_details',
];

// Enhanced status color mapping for better visual hierarchy
const STATUS_COLORS: Record<
	DelegationStatus,
	{bg: string; text: string; border: string}
> = {
	Planned: {
		bg: 'bg-gradient-to-br from-blue-50 to-blue-100',
		text: 'text-blue-700',
		border: 'border-blue-200',
	},
	Confirmed: {
		bg: 'bg-gradient-to-br from-green-50 to-green-100',
		text: 'text-green-700',
		border: 'border-green-200',
	},
	In_Progress: {
		bg: 'bg-gradient-to-br from-yellow-50 to-yellow-100',
		text: 'text-yellow-700',
		border: 'border-yellow-200',
	},
	Completed: {
		bg: 'bg-gradient-to-br from-purple-50 to-purple-100',
		text: 'text-purple-700',
		border: 'border-purple-200',
	},
	Cancelled: {
		bg: 'bg-gradient-to-br from-red-50 to-red-100',
		text: 'text-red-700',
		border: 'border-red-200',
	},
	No_details: {
		bg: 'bg-gradient-to-br from-gray-50 to-gray-100',
		text: 'text-gray-700',
		border: 'border-gray-200',
	},
};

// Format status for display (replace underscores with spaces)
const formatStatus = (status: DelegationStatus): string => {
	return status.replace('_', ' ');
};

/**
 * Props for the DelegationSummary component
 */
interface DelegationSummaryProps {
	/** Array of delegations to display summary statistics for */
	delegations: Delegation[];
	/** Additional CSS class names */
	className?: string;
}

/**
 * A component that displays summary statistics for a list of delegations
 *
 * @example
 * ```tsx
 * <DelegationSummary delegations={filteredDelegations} />
 * ```
 */
export function DelegationSummary({
	delegations,
	className,
}: DelegationSummaryProps) {
	// Calculate statistics
	const total = delegations.length;

	// Count delegations by status
	const statusCounts = delegations.reduce((acc, delegation) => {
		const status = delegation.status;
		acc[status] = (acc[status] || 0) + 1;
		return acc;
	}, {} as Record<DelegationStatus, number>);

	// Calculate total delegates
	const totalDelegates = delegations.reduce((acc, delegation) => {
		return acc + delegation.delegates.length;
	}, 0);

	// Get list of statuses that have at least one delegation, sorted by count
	const activeStatuses = Object.entries(statusCounts)
		.sort(([, a], [, b]) => b - a)
		.map(([status]) => status as DelegationStatus);

	return (
		<div className={cn('mt-6 mb-8', className)}>
			{/* Main summary grid */}
			<div className='grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6'>
				{/* Total Delegations Card */}
				<SummaryCard
					value={total}
					label='Total Delegations'
					className='bg-gradient-to-br from-slate-50 to-slate-100 border-slate-200 shadow-sm hover:shadow-md transition-shadow'
					textColor='text-slate-700'
					valueColor='text-slate-800'
				/>

				{/* Total Delegates Card */}
				<SummaryCard
					value={totalDelegates}
					label='Total Delegates'
					className='bg-gradient-to-br from-indigo-50 to-indigo-100 border-indigo-200 shadow-sm hover:shadow-md transition-shadow'
					textColor='text-indigo-700'
					valueColor='text-indigo-800'
				/>

				{/* Top 2 status cards */}
				{activeStatuses.slice(0, 2).map((status) => {
					const statusConfig = STATUS_COLORS[status];
					return (
						<SummaryCard
							key={status}
							value={statusCounts[status]}
							label={formatStatus(status)}
							className={cn(
								statusConfig.bg,
								statusConfig.border,
								'shadow-sm hover:shadow-md transition-shadow'
							)}
							textColor={statusConfig.text}
							valueColor={statusConfig.text}
						/>
					);
				})}
			</div>

			{/* Additional status breakdown if there are more than 2 active statuses */}
			{activeStatuses.length > 2 && (
				<div className='grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3'>
					{activeStatuses.slice(2).map((status) => {
						const statusConfig = STATUS_COLORS[status];
						return (
							<SummaryCard
								key={status}
								value={statusCounts[status]}
								label={formatStatus(status)}
								className={cn(
									statusConfig.bg,
									statusConfig.border,
									'shadow-sm hover:shadow-md transition-shadow'
								)}
								textColor={statusConfig.text}
								valueColor={statusConfig.text}
								compact={true}
							/>
						);
					})}
				</div>
			)}
		</div>
	);
}

/**
 * Props for the SummaryCard component
 */
interface SummaryCardProps {
	/** Value to display (typically a number) */
	value: number;
	/** Label to display below the value */
	label: string;
	/** Additional CSS class for the card background */
	className?: string;
	/** CSS class for the label text color */
	textColor?: string;
	/** CSS class for the value text color */
	valueColor?: string;
	/** Whether to use compact layout */
	compact?: boolean;
}

/**
 * A card component for displaying a summary statistic
 */
function SummaryCard({
	value,
	label,
	className,
	textColor = 'text-gray-600',
	valueColor = 'text-gray-800',
	compact = false,
}: SummaryCardProps) {
	return (
		<Card
			className={cn(
				'overflow-hidden border transition-all duration-200',
				className
			)}>
			<CardContent className={cn('text-center', compact ? 'p-3' : 'p-4')}>
				<div
					className={cn(
						'font-bold',
						compact ? 'text-xl mb-1' : 'text-3xl mb-2',
						valueColor
					)}>
					{value.toLocaleString()}
				</div>
				<div
					className={cn(
						'font-medium',
						compact ? 'text-xs' : 'text-sm',
						textColor
					)}>
					{label}
				</div>
			</CardContent>
		</Card>
	);
}
