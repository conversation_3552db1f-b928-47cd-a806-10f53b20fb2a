/**
 * Admin Routes Integration Tests
 *
 * This module tests the admin routes functionality.
 */

import request from 'supertest';
import express from 'express';
import adminRoutes from '../routes/admin.routes';
import * as adminController from '../controllers/admin.controller';
import { jest, describe, it, expect, beforeEach } from '@jest/globals';

// Mock dependencies
jest.mock('../controllers/admin.controller');

describe('Admin Routes', () => {
	let app: express.Application;

	beforeEach(() => {
		app = express();
		app.use(express.json());
		app.use('/api/admin', adminRoutes);
		jest.clearAllMocks();
	});

	describe('GET /api/admin/health', () => {
		it('should call the health status controller', async () => {
			// Mock controller implementation
			(adminController.getHealthStatus as jest.Mock).mockImplementation(
				(req, res) => {
					res.status(200).json({
						status: 'success',
						data: {
							status: 'UP',
							message: 'Backend service is healthy',
						},
					});
				}
			);

			// Make request
			const response = await request(app).get('/api/admin/health');

			// Verify response
			expect(response.status).toBe(200);
			expect(response.body).toEqual({
				status: 'success',
				data: {
					status: 'UP',
					message: 'Backend service is healthy',
				},
			});
			expect(adminController.getHealthStatus).toHaveBeenCalled();
		});
	});

	describe('GET /api/admin/performance', () => {
		it('should call the performance metrics controller', async () => {
			// Mock controller implementation
			(adminController.getPerformanceMetrics as jest.Mock).mockImplementation(
				(req, res) => {
					res.status(200).json({
						status: 'success',
						data: {
							cacheHitRate: {
								indexHitRate: 90,
								tableHitRate: 85,
							},
							connectionCount: 5,
							activeQueries: 2,
							avgQueryTime: 1.5,
							timestamp: '2023-01-01T00:00:00.000Z',
						},
					});
				}
			);

			// Make request
			const response = await request(app).get('/api/admin/performance');

			// Verify response
			expect(response.status).toBe(200);
			expect(response.body).toEqual({
				status: 'success',
				data: {
					cacheHitRate: {
						indexHitRate: 90,
						tableHitRate: 85,
					},
					connectionCount: 5,
					activeQueries: 2,
					avgQueryTime: 1.5,
					timestamp: '2023-01-01T00:00:00.000Z',
				},
			});
			expect(adminController.getPerformanceMetrics).toHaveBeenCalled();
		});
	});

	describe('GET /api/admin/errors', () => {
		it('should call the error logs controller with default parameters', async () => {
			// Mock controller implementation
			(adminController.getErrorLogs as jest.Mock).mockImplementation(
				(req, res) => {
					res.status(200).json({
						status: 'success',
						data: [],
						pagination: {
							page: 1,
							limit: 10,
							total: 0,
							totalPages: 0,
						},
					});
				}
			);

			// Make request
			const response = await request(app).get('/api/admin/errors');

			// Verify response
			expect(response.status).toBe(200);
			expect(response.body).toEqual({
				status: 'success',
				data: [],
				pagination: {
					page: 1,
					limit: 10,
					total: 0,
					totalPages: 0,
				},
			});
			expect(adminController.getErrorLogs).toHaveBeenCalled();
		});

		it('should call the error logs controller with custom parameters', async () => {
			// Mock controller implementation
			(adminController.getErrorLogs as jest.Mock).mockImplementation(
				(req, res) => {
					res.status(200).json({
						status: 'success',
						data: [],
						pagination: {
							page: 2,
							limit: 5,
							total: 0,
							totalPages: 0,
						},
					});
				}
			);

			// Make request
			const response = await request(app).get(
				'/api/admin/errors?page=2&limit=5&level=ERROR'
			);

			// Verify response
			expect(response.status).toBe(200);
			expect(response.body).toEqual({
				status: 'success',
				data: [],
				pagination: {
					page: 2,
					limit: 5,
					total: 0,
					totalPages: 0,
				},
			});
			expect(adminController.getErrorLogs).toHaveBeenCalled();
		});

		it('should return 400 for invalid parameters', async () => {
			// Make request with invalid parameters
			const response = await request(app).get(
				'/api/admin/errors?page=invalid&limit=invalid'
			);

			// Verify response
			expect(response.status).toBe(400);
			expect(response.body).toHaveProperty('status', 'error');
			expect(response.body).toHaveProperty('message', 'Validation failed');
			expect(response.body).toHaveProperty('errors');
		});
	});
});
