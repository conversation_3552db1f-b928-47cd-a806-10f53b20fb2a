'use client';

import {useEffect, useState} from 'react';
import {useParams, useRouter} from 'next/navigation';
import EmployeeForm from '@/components/employees/EmployeeForm';
import {
	getEmployeeById,
	updateEmployee as storeUpdateEmployee,
} from '@/lib/store';
import type {EmployeeFormData} from '@/lib/schemas/employeeSchemas';
import type {Employee} from '@/lib/types';
import {PageHeader} from '@/components/ui/PageHeader';
import {UserCog} from 'lucide-react';
import {useToast} from '@/hooks/use-toast';
import {Skeleton} from '@/components/ui/skeleton';

export default function EditEmployeePage() {
	const router = useRouter();
	const params = useParams();
	const {toast} = useToast();
	const [employee, setEmployee] = useState<Employee | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [isSubmitting, setIsSubmitting] = useState(false);

	const employeeId = params.id as string;

	useEffect(() => {
		const fetchEmployee = async () => {
			if (employeeId) {
				try {
					const fetchedEmployee = await getEmployeeById(Number(employeeId));
					if (fetchedEmployee) {
						setEmployee(fetchedEmployee);
					} else {
						toast({
							title: 'Error',
							description: 'Employee not found.',
							variant: 'destructive',
						});
						router.push('/employees');
					}
				} catch (error) {
					console.error('Failed to fetch employee:', error);
					toast({
						title: 'Error',
						description: 'Failed to load employee data.',
						variant: 'destructive',
					});
					router.push('/employees');
				} finally {
					setIsLoading(false);
				}
			}
		};

		fetchEmployee();
	}, [employeeId, router, toast]);

	const handleSubmit = async (data: EmployeeFormData) => {
		if (!employeeId) {
			toast({
				title: 'Error',
				description: 'Invalid employee ID.',
				variant: 'destructive',
			});
			return;
		}

		setIsSubmitting(true);

		try {
			console.log(`Updating employee ID: ${employeeId}`, data);

			// Clean up the data to handle null/undefined values properly
			const cleanedData = {
				...data,
				statusChangeReason: data.statusChangeReason || undefined,
			};

			const updatedEmployee = await storeUpdateEmployee(
				Number(employeeId),
				cleanedData
			);

			toast({
				title: 'Employee Updated Successfully',
				description: `${
					data.fullName || data.name
				} has been updated with the latest information.`,
				variant: 'default',
			});

			// Navigate back to employee detail page
			router.push(`/employees/${employeeId}`);
		} catch (error: any) {
			console.error('Failed to update employee:', error);

			// Enhanced error message based on error type
			let errorMessage = 'Failed to update employee. Please try again.';

			if (error.message?.includes('Network error')) {
				errorMessage =
					'Network error. Please check your connection and try again.';
			} else if (error.message?.includes('404')) {
				errorMessage = 'Employee not found. They may have been deleted.';
			} else if (error.message?.includes('403')) {
				errorMessage = 'You do not have permission to edit this employee.';
			} else if (error.message?.includes('Validation failed')) {
				errorMessage =
					'Please check your input data. Some fields may be invalid.';
			} else if (error.validationErrors) {
				// Handle validation errors array
				const validationMessages = error.validationErrors
					.map((err: any) => `${err.path}: ${err.message}`)
					.join(', ');
				errorMessage = `Validation errors: ${validationMessages}`;
			} else if (error.response?.data?.error) {
				errorMessage = error.response.data.error;
			} else if (error.message) {
				errorMessage = error.message;
			}

			toast({
				title: 'Update Failed',
				description: errorMessage,
				variant: 'destructive',
			});
		} finally {
			setIsSubmitting(false);
		}
	};

	if (isLoading) {
		return (
			<div className='space-y-6'>
				<PageHeader title='Loading...' icon={UserCog} />
				<Skeleton className='h-[700px] w-full rounded-lg bg-card' />
			</div>
		);
	}

	if (!employee) {
		return <p>Employee not found.</p>;
	}

	return (
		<div className='space-y-6'>
			<PageHeader
				title={`Edit Employee: ${employee.fullName}`}
				description='Modify the details for this employee.'
				icon={UserCog}
			/>
			<EmployeeForm
				onSubmit={handleSubmit}
				initialData={employee}
				isEditing={true}
				isLoading={isSubmitting}
			/>
		</div>
	);
}
