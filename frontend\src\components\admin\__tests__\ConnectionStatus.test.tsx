import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ConnectionStatus } from '../ConnectionStatus';
import { getMockHealthStatus } from '@/lib/adminService';
import { useApi } from '@/hooks/useApi';

// Mock the hooks
jest.mock('@/hooks/useApi', () => ({
  useApi: jest.fn(),
}));

// Mock the components
jest.mock('@/components/ui/loading-states', () => ({
  LoadingError: ({ message, onRetry }: { message: string; onRetry: () => void }) => (
    <div data-testid="loading-error">
      <p>{message}</p>
      <button onClick={onRetry}>Retry</button>
    </div>
  ),
}));

jest.mock('@/components/ui/error-boundary', () => ({
  ErrorBoundary: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

describe('ConnectionStatus Component', () => {
  const mockRefetch = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('should render loading state correctly', () => {
    (useApi as jest.Mock).mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
      refetch: mockRefetch,
    });
    
    render(<ConnectionStatus />);
    
    expect(screen.getAllByTestId('skeleton')).toHaveLength(3);
  });
  
  it('should render error state correctly', () => {
    (useApi as jest.Mock).mockReturnValue({
      data: null,
      isLoading: false,
      error: 'Failed to fetch connection status',
      refetch: mockRefetch,
    });
    
    render(<ConnectionStatus />);
    
    expect(screen.getByTestId('loading-error')).toBeInTheDocument();
    expect(screen.getByText('Failed to fetch connection status')).toBeInTheDocument();
    
    // Test retry functionality
    fireEvent.click(screen.getByText('Retry'));
    expect(mockRefetch).toHaveBeenCalledTimes(1);
  });
  
  it('should render connection status correctly', () => {
    const mockHealth = getMockHealthStatus();
    
    (useApi as jest.Mock).mockReturnValue({
      data: mockHealth,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
    });
    
    render(<ConnectionStatus />);
    
    // Check overall status
    expect(screen.getByText('Overall Status:')).toBeInTheDocument();
    expect(screen.getByText('Connected')).toBeInTheDocument();
    
    // Check database status
    expect(screen.getByText('Database:')).toBeInTheDocument();
    expect(screen.getByText(mockHealth.components.database.type)).toBeInTheDocument();
    
    // Check Supabase status
    expect(screen.getByText('Supabase:')).toBeInTheDocument();
    expect(screen.getByText(mockHealth.components.supabase.url)).toBeInTheDocument();
    
    // Check timestamp
    expect(screen.getByText(`Last updated: ${new Date(mockHealth.timestamp).toLocaleString()}`)).toBeInTheDocument();
  });
  
  it('should display component errors when present', () => {
    const mockHealth = getMockHealthStatus();
    mockHealth.components.database.error = 'Database connection error';
    mockHealth.components.supabase.error = 'Supabase connection error';
    
    (useApi as jest.Mock).mockReturnValue({
      data: mockHealth,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
    });
    
    render(<ConnectionStatus />);
    
    expect(screen.getByText('Error: Database connection error')).toBeInTheDocument();
    expect(screen.getByText('Error: Supabase connection error')).toBeInTheDocument();
  });
  
  it('should display version and uptime when available', () => {
    const mockHealth = getMockHealthStatus();
    
    (useApi as jest.Mock).mockReturnValue({
      data: mockHealth,
      isLoading: false,
      error: null,
      refetch: mockRefetch,
    });
    
    render(<ConnectionStatus />);
    
    expect(screen.getByText('Version:')).toBeInTheDocument();
    expect(screen.getByText(mockHealth.version)).toBeInTheDocument();
    
    expect(screen.getByText('Uptime:')).toBeInTheDocument();
    const hours = Math.floor(mockHealth.uptime / 3600);
    const minutes = Math.floor((mockHealth.uptime % 3600) / 60);
    expect(screen.getByText(`${hours}h ${minutes}m`)).toBeInTheDocument();
  });
  
  it('should handle refresh button correctly', async () => {
    (useApi as jest.Mock).mockReturnValue({
      data: getMockHealthStatus(),
      isLoading: false,
      error: null,
      refetch: mockRefetch,
    });
    
    render(<ConnectionStatus />);
    
    // Test refresh button
    fireEvent.click(screen.getByText('Refresh Status'));
    expect(mockRefetch).toHaveBeenCalledTimes(1);
  });
});
