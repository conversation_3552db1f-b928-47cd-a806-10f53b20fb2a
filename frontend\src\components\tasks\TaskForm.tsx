'use client';

import {useForm, type SubmitHandler} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {
	TaskSchema,
	type TaskFormData,
	TaskStatusSchema,
	TaskPrioritySchema,
} from '@/lib/schemas/taskSchemas';
import type {Task, Employee, Vehicle} from '@/lib/types';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Textarea} from '@/components/ui/textarea';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardDescription,
	CardFooter,
} from '@/components/ui/card';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {
	Save,
	ArrowLeft,
	ClipboardList,
	Clock,
	AlertTriangle,
} from 'lucide-react';
import {useRouter} from 'next/navigation';
import {useToast} from '@/hooks/use-toast';
import {getEmployees, getVehicles} from '@/lib/store';
import {useEffect, useState} from 'react';
import {
	formatDateForInput,
	formatDateForApi,
	formatDateForDisplay,
} from '@/lib/utils/dateUtils';
import {format} from 'date-fns';

interface TaskFormProps {
	onSubmit: (data: TaskFormData) => void;
	initialData?: Task;
	isEditing?: boolean;
}

// Local formatDateForInput function has been replaced by the imported one from dateUtils

const UNASSIGNED_VALUE = '__UNASSIGNED__'; // Represents no selection for employee
// Use 'as const' to make TypeScript recognize this as a specific string literal type
const NO_SPECIFIC_VEHICLE_VALUE = '__NO_VEHICLE__' as const; // Represents no selection for vehicle

export default function TaskForm({
	onSubmit,
	initialData,
	isEditing = false,
}: TaskFormProps) {
	const router = useRouter();
	const {toast} = useToast();
	const [employees, setEmployees] = useState<Employee[]>([]);
	const [vehicles, setVehicles] = useState<Vehicle[]>([]);

	const assignableEmployees = employees.filter(
		(emp) => emp.status === 'Active'
	);

	useEffect(() => {
		// These store functions are now async
		const fetchData = async () => {
			try {
				const empData = await getEmployees();
				setEmployees(empData || []);
				const vehData = await getVehicles();
				setVehicles(vehData || []);
			} catch (error) {
				console.error('Failed to fetch employees or vehicles for form:', error);
				toast({
					title: 'Error',
					description: 'Could not load employees/vehicles for form.',
					variant: 'destructive',
				});
			}
		};
		fetchData();
	}, [toast]);

	const form = useForm<TaskFormData>({
		resolver: zodResolver(TaskSchema),
		defaultValues: {
			description: initialData?.description || '',
			location: initialData?.location || '',
			dateTime: initialData
				? formatDateForInput(initialData.dateTime, 'datetime-local')
				: format(new Date(), "yyyy-MM-dd'T'HH:mm"),
			estimatedDuration: initialData?.estimatedDuration || 60,
			requiredSkills: initialData?.requiredSkills || [],
			priority: initialData?.priority || 'Medium',
			deadline: initialData?.deadline
				? formatDateForInput(initialData.deadline, 'datetime-local')
				: '',
			status: initialData?.status || 'Pending',
			// assignedEmployeeIds in TaskFormData is number[], initialData.assignedTo is string[] from server
			assignedEmployeeIds:
				initialData?.assignedTo
					?.map((id) => Number(id))
					.filter((id) => !isNaN(id)) || [],
			notes: initialData?.notes || '',
			// vehicleId in TaskFormData is number, initialData.vehicleId is string from server
			vehicleId: initialData?.vehicleId ? Number(initialData.vehicleId) : null,
		},
	});

	const handleFormSubmit: SubmitHandler<TaskFormData> = (data) => {
		const submissionData: TaskFormData = {
			...data,
			// Convert dateTime to proper ISO format using our utility function
			dateTime: formatDateForApi(data.dateTime),
			// Handle deadline - convert to ISO or undefined
			deadline: data.deadline ? formatDateForApi(data.deadline) : undefined,
			// Handle vehicleId - convert to number or null
			vehicleId: data.vehicleId ? Number(data.vehicleId) : null,
			// Ensure assignedEmployeeIds are numbers
			assignedEmployeeIds: Array.isArray(data.assignedEmployeeIds)
				? data.assignedEmployeeIds.map((id) => Number(id))
				: [],
		};
		onSubmit(submissionData);
	};

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(handleFormSubmit)}>
				<Card className='shadow-lg'>
					<CardHeader>
						<CardTitle className='text-2xl text-primary'>
							{isEditing ? 'Edit Task' : 'Add New Task'}
						</CardTitle>
						<CardDescription>Enter the details for the task.</CardDescription>
					</CardHeader>
					<CardContent className='space-y-6'>
						{/* Basic Info */}
						<section className='space-y-4 p-4 border rounded-lg bg-card'>
							<h3 className='text-lg font-semibold text-foreground flex items-center'>
								<ClipboardList className='mr-2 h-5 w-5 text-accent' />
								Task Details
							</h3>
							<FormField
								control={form.control}
								name='description'
								render={({field}) => (
									<FormItem>
										<FormLabel>Description</FormLabel>
										<FormControl>
											<Textarea
												placeholder='e.g., Pick up package from Warehouse A'
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name='location'
								render={({field}) => (
									<FormItem>
										<FormLabel>Location</FormLabel>
										<FormControl>
											<Input
												placeholder='e.g., 123 Main St, City Center'
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
								<FormField
									control={form.control}
									name='dateTime'
									render={({field}) => (
										<FormItem>
											<FormLabel>Start Date & Time</FormLabel>
											<FormControl>
												<Input type='datetime-local' {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name='estimatedDuration'
									render={({field}) => (
										<FormItem>
											<FormLabel>Estimated Duration (minutes)</FormLabel>
											<FormControl>
												<Input
													type='number'
													placeholder='e.g., 60'
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</section>

						{/* Scheduling & Assignment */}
						<section className='space-y-4 p-4 border rounded-lg bg-card'>
							<h3 className='text-lg font-semibold text-foreground flex items-center'>
								<Clock className='mr-2 h-5 w-5 text-accent' />
								Scheduling & Assignment
							</h3>
							<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
								<FormField
									control={form.control}
									name='priority'
									render={({field}) => (
										<FormItem>
											<FormLabel>Priority</FormLabel>
											<Select
												onValueChange={field.onChange}
												defaultValue={field.value}>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder='Select priority' />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													{TaskPrioritySchema.options.map((priority) => (
														<SelectItem key={priority} value={priority}>
															{priority}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name='deadline'
									render={({field}) => (
										<FormItem>
											<FormLabel>Deadline (Optional)</FormLabel>
											<FormControl>
												<Input
													type='datetime-local'
													{...field}
													value={field.value || ''}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
							<FormField
								control={form.control}
								name='status'
								render={({field}) => (
									<FormItem>
										<FormLabel>Status</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder='Select status' />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{TaskStatusSchema.options.map((status) => (
													<SelectItem key={status} value={status}>
														{status}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name='assignedEmployeeIds'
								render={({field}) => (
									<FormItem>
										<FormLabel>Assign to Employees (Optional)</FormLabel>
										<Select
											onValueChange={(value) => {
												// For multi-select, this would be more complex
												// For now, assuming single select for simplicity matching backend Prisma.TaskUpdateInput for assignedEmployees.set
												// This will need to be updated if multiple employees can be assigned via this form field.
												// The schema uses `assignedEmployeeIds: z.array(z.number())`
												// This means `field.onChange` expects an array of numbers.
												// If your Select allows multiple, you need to manage that array.
												// If it's single select, then `value` is a single string ID or UNASSIGNED_VALUE.
												if (value === UNASSIGNED_VALUE) {
													field.onChange([]);
												} else if (value) {
													field.onChange([Number(value)]); // Store as array of numbers
												} else {
													field.onChange([]);
												}
											}}
											// Value for Select needs to be a string or undefined for single select.
											// If field.value is an array, take the first one or UNASSIGNED_VALUE.
											value={
												field.value && field.value.length > 0
													? String(field.value[0])
													: UNASSIGNED_VALUE
											}>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder='Select an employee (optional)' />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												<SelectItem value={UNASSIGNED_VALUE}>
													Unassigned
												</SelectItem>
												{assignableEmployees.map((emp) => {
													// Pre-compute vehicle info for drivers to avoid duplicate lookups
													const vehicleInfo =
														emp.role === 'driver' && emp.vehicleId
															? vehicles.find(
																	(v) => v.id === Number(emp.vehicleId)
															  )
															: null;

													return (
														<SelectItem key={emp.id} value={String(emp.id)}>
															{emp.fullName} (
															{emp.role.charAt(0).toUpperCase() +
																emp.role.slice(1).replace('_', ' ')}
															, {emp.status}
															{emp.role === 'driver' && emp.availability && (
																<span>
																	, {emp.availability.replace('_', ' ')}
																</span>
															)}
															{emp.role === 'driver' && emp.currentLocation && (
																<span>, @ {emp.currentLocation}</span>
															)}
															{emp.role === 'driver' && (
																<span>
																	, Vehicle:{' '}
																	{vehicleInfo
																		? `${vehicleInfo.make} ${vehicleInfo.model}`
																		: 'None'}
																</span>
															)}
															)
														</SelectItem>
													);
												})}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
						</section>

						{/* Additional Info */}
						<section className='space-y-4 p-4 border rounded-lg bg-card'>
							<h3 className='text-lg font-semibold text-foreground flex items-center'>
								<AlertTriangle className='mr-2 h-5 w-5 text-accent' />
								Additional Information
							</h3>
							<FormField
								control={form.control}
								name='requiredSkills'
								render={({field}) => (
									<FormItem>
										<FormLabel>
											Required Skills (Optional, comma-separated)
										</FormLabel>
										<FormControl>
											<Input
												placeholder='e.g., Forklift License, Customer Service'
												value={
													Array.isArray(field.value)
														? field.value.join(', ')
														: ''
												}
												onChange={(e) =>
													field.onChange(
														e.target.value
															.split(',')
															.map((skill) => skill.trim())
															.filter((skill) => skill)
													)
												}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name='vehicleId'
								render={({field}) => (
									<FormItem>
										<FormLabel>Associated Vehicle (Optional)</FormLabel>
										<Select
											onValueChange={(value) => {
												// Convert to null if it's the "no vehicle" option, otherwise convert to number
												field.onChange(
													value === NO_SPECIFIC_VEHICLE_VALUE
														? null
														: Number(value)
												);
											}}
											value={
												// Convert null to the "no vehicle" option string, otherwise convert number to string
												field.value === null
													? NO_SPECIFIC_VEHICLE_VALUE
													: field.value
													? String(field.value)
													: undefined
											}>
											<FormControl>
												<SelectTrigger>
													<SelectValue placeholder='Select a vehicle' />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												<SelectItem value={NO_SPECIFIC_VEHICLE_VALUE}>
													No Specific Vehicle
												</SelectItem>
												{vehicles.map((vehicle) => (
													<SelectItem
														key={vehicle.id}
														value={String(vehicle.id)}>
														{vehicle.make} {vehicle.model} ({vehicle.year}) -{' '}
														{vehicle.licensePlate || 'N/A'}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name='notes'
								render={({field}) => (
									<FormItem>
										<FormLabel>Notes (Optional)</FormLabel>
										<FormControl>
											<Textarea
												placeholder='e.g., Gate code is 1234, contact person: Jane Smith'
												{...field}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</section>
					</CardContent>
					<CardFooter className='flex justify-between gap-2 border-t pt-6'>
						<Button
							type='button'
							variant='outline'
							onClick={() => router.back()}>
							<ArrowLeft className='mr-2 h-4 w-4' />
							Cancel
						</Button>
						<Button
							type='submit'
							className='bg-accent text-accent-foreground hover:bg-accent/90'>
							<Save className='mr-2 h-4 w-4' />
							{isEditing ? 'Save Changes' : 'Create Task'}
						</Button>
					</CardFooter>
				</Card>
			</form>
		</Form>
	);
}
