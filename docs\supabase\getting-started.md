# Getting Started with Supabase

This guide will help you get started with Supa<PERSON> in the Car Service Tracking
System application.

## Prerequisites

Before you begin, make sure you have:

- A Supabase account (sign up at [supabase.com](https://supabase.com))
- The Car Service Tracking System codebase
- Node.js and npm installed

## Creating a Supabase Project

1. **Sign up or log in to Supabase**:

   - Go to [app.supabase.com](https://app.supabase.com)
   - Create an account or log in with your existing account

2. **Create a new project**:

   - Click "New Project"
   - Choose an organization (create one if needed)
   - Enter a name for your project
   - Set a secure database password (save this password!)
   - Choose a region closest to your users
   - Click "Create new project"

3. **Get your project credentials**:
   - Go to Project Settings > API
   - Note your Project URL and anon key
   - Go to Project Settings > Database
   - Note your PostgreSQL connection string
   - Find your project ID in the URL of your Supabase dashboard:
     `https://app.supabase.com/project/[your-project-id]`

## Linking Your Project

Now that you have a Supabase project, you need to link it to your local project:

1. **Login to Supabase CLI**:

   ```bash
   cd backend
   npm run supabase:login
   ```

2. **Link your project**:

   ```bash
   npm run supabase:link
   ```

   You will be prompted to enter:

   - Your Supabase project ID
   - Your database password
   - Your service role key (found in Project Settings > API)

3. **Verify the link**:
   ```bash
   npm run supabase:config
   ```

## Initial Database Setup

After linking your project, you need to set up your database:

1. **Push your Prisma schema to Supabase**:

   ```bash
   npm run supabase:push
   ```

2. **Or pull the existing schema from Supabase**:

   ```bash
   npm run supabase:pull
   ```

3. **Run migrations (if you have existing migrations)**:

   ```bash
   npm run supabase:migrate
   ```

4. **Seed the database (optional)**:

   ```bash
   npm run supabase:seed
   ```

5. **Switch to using Supabase**:
   ```bash
   npm run db:supabase
   ```

## Troubleshooting

### Connection Issues

If you're having trouble connecting to Supabase:

1. **Check your credentials**:

   - Verify your project ID, database password, and service role key
   - Ensure they are correctly entered in `supabase/config.json`

2. **Check network restrictions**:

   - Ensure your IP address is allowed in Supabase's network restrictions
   - Go to Project Settings > API > API Settings > JWT Settings

3. **Check database status**:
   - Go to Project Settings > Database
   - Ensure your database is healthy and not in maintenance mode

### Environment Variables

If your application can't connect to Supabase:

1. **Check your .env file**:

   - Ensure `USE_SUPABASE` is set to `true`
   - Verify that `DATABASE_URL` points to your Supabase database
   - Check that `SUPABASE_URL` and `SUPABASE_KEY` are set correctly

2. **Restart your application**:
   - After changing environment variables, restart your application

## Next Steps

After successfully connecting to Supabase, you might want to:

1. **Explore Supabase features**:

   - Authentication
   - Storage
   - Real-time subscriptions
   - Edge Functions

2. **Update your application**:
   - Implement Supabase-specific features
   - Test all API endpoints with Supabase
   - Update your deployment configuration
