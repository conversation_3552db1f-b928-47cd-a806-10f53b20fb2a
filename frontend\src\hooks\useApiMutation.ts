'use client';

import { useState, useCallback } from 'react';
import { ApiError } from '@/lib/types/api';

interface UseApiMutationOptions<TData, TVariables> {
  onSuccess?: (data: TData, variables: TVariables) => void;
  onError?: (error: Error, variables: TVariables) => void;
  onSettled?: (data: TData | undefined, error: Error | null, variables: TVariables) => void;
  retry?: number | boolean;
  retryDelay?: number | ((attempt: number) => number);
}

interface UseApiMutationResult<TData, TVariables> {
  data: TData | undefined;
  error: Error | null;
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
  mutate: (variables: TVariables) => Promise<TData | undefined>;
  reset: () => void;
}

/**
 * Custom hook for API mutations (create, update, delete operations)
 * 
 * @param mutationFn - Function that accepts variables and returns a promise with the result
 * @param options - Configuration options
 * @returns Mutation result object
 */
function useApiMutation<TData, TVariables>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options: UseApiMutationOptions<TData, TVariables> = {}
): UseApiMutationResult<TData, TVariables> {
  const {
    onSuccess,
    onError,
    onSettled,
    retry = 0,
    retryDelay = 1000,
  } = options;

  const [data, setData] = useState<TData | undefined>(undefined);
  const [error, setError] = useState<Error | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Calculate retry delay based on attempt number
  const getRetryDelay = useCallback(
    (attempt: number): number => {
      if (typeof retryDelay === 'function') {
        return retryDelay(attempt);
      }
      // Exponential backoff with jitter
      const baseDelay = typeof retryDelay === 'number' ? retryDelay : 1000;
      const exponentialDelay = baseDelay * Math.pow(2, attempt);
      const jitter = Math.random() * 0.5 + 0.5; // 0.5-1.5 multiplier
      return Math.min(exponentialDelay * jitter, 30000); // Cap at 30 seconds
    },
    [retryDelay]
  );

  // Mutation function
  const mutate = useCallback(
    async (variables: TVariables): Promise<TData | undefined> => {
      setIsLoading(true);
      setError(null);

      const performMutation = async (attempt: number = 0): Promise<TData | undefined> => {
        try {
          const result = await mutationFn(variables);
          setData(result);
          if (onSuccess) onSuccess(result, variables);
          if (onSettled) onSettled(result, null, variables);
          return result;
        } catch (err) {
          const error = err instanceof Error ? err : new Error(String(err));
          
          // Log error details
          if (error instanceof ApiError) {
            console.error(`API Error (${error.status}):`, error.message);
            if (error.validationErrors) {
              console.error('Validation errors:', error.validationErrors);
            }
          } else {
            console.error('Mutation error:', error);
          }

          // Handle retries
          const maxRetries = typeof retry === 'boolean' ? (retry ? 3 : 0) : retry;
          if (attempt < maxRetries) {
            // Only retry on network errors or server errors (5xx), not client errors (4xx)
            const shouldRetry = !(error instanceof ApiError && error.status >= 400 && error.status < 500);
            
            if (shouldRetry) {
              const delay = getRetryDelay(attempt);
              console.warn(`Retrying mutation (attempt ${attempt + 1}/${maxRetries}) after ${delay}ms`);
              
              await new Promise(resolve => setTimeout(resolve, delay));
              return performMutation(attempt + 1);
            }
          }
          
          // If we've exhausted retries or shouldn't retry, set the error
          setError(error);
          if (onError) onError(error, variables);
          if (onSettled) onSettled(undefined, error, variables);
          throw error;
        }
      };

      try {
        const result = await performMutation();
        return result;
      } finally {
        setIsLoading(false);
      }
    },
    [mutationFn, onSuccess, onError, onSettled, retry, getRetryDelay]
  );

  // Reset function
  const reset = useCallback(() => {
    setData(undefined);
    setError(null);
    setIsLoading(false);
  }, []);

  return {
    data,
    error,
    isLoading,
    isError: !!error,
    isSuccess: !!data && !error,
    mutate,
    reset,
  };
}

export default useApiMutation;
