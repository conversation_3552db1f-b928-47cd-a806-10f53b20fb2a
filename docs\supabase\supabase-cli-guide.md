# Supabase CLI Guide for Prisma Projects

This guide explains how to use the Supabase CLI with your Prisma-based project to connect to and manage your Supabase PostgreSQL database.

## Overview

The Supabase CLI integration allows you to:

1. Connect your Prisma ORM to a Supabase PostgreSQL database
2. Pull database schema from Supabase into your Prisma schema
3. Push your Prisma schema to Supabase
4. Run migrations on your Supabase database
5. Seed your Supabase database with initial data

## Prerequisites

- A Supabase account and project
- Your Supabase project ID, database password, and service role key

## Setup Instructions

### 1. Login to Supabase

First, login to your Supabase account:

```bash
npm run supabase:login
```

This will open a browser window where you can authenticate with Supa<PERSON>.

### 2. Link Your Project to Supabase

Link your local project to your Supabase project:

```bash
npm run supabase:link
```

You will be prompted to enter:
- Your Supabase project ID
- Your database password
- Your service role key

This information will be saved in `supabase/config.json` and your `.env` file will be updated with the Supabase database URL.

### 3. Working with Your Database

#### Pull Database Schema from Supabase

To pull the current database schema from Supabase into your Prisma schema:

```bash
npm run supabase:pull
```

This will introspect your Supabase database and update your Prisma schema accordingly.

#### Push Prisma Schema to Supabase

To push your Prisma schema to Supabase:

```bash
npm run supabase:push
```

This will apply your Prisma schema changes directly to your Supabase database.

#### Run Migrations on Supabase

To run Prisma migrations on your Supabase database:

```bash
npm run supabase:migrate
```

This will apply any pending migrations to your Supabase database.

#### Seed Supabase Database

To seed your Supabase database with initial data:

```bash
npm run supabase:seed
```

This will run your Prisma seed script against your Supabase database.

### 4. View Current Configuration

To view your current Supabase configuration:

```bash
npm run supabase:config
```

This will display your current Supabase configuration (with sensitive information masked).

## Finding Your Supabase Credentials

### Project ID

Your Supabase project ID can be found in the URL of your Supabase dashboard:
```
https://app.supabase.com/project/[your-project-id]
```

### Database Password

This is the password you set when creating your Supabase project. If you've forgotten it:

1. Go to your Supabase dashboard
2. Navigate to Project Settings > Database
3. Find the "Database Password" section
4. Click "Reset Password" if needed

### Service Role Key

To get your service role key:

1. Go to your Supabase dashboard
2. Navigate to Project Settings > API
3. Find the "Project API keys" section
4. Copy the "service_role" key (this has full access to your database)

## Security Considerations

- The `service_role` key has full access to your database, so keep it secure
- Never commit your `supabase/config.json` file to version control
- Add `supabase/config.json` to your `.gitignore` file

## Switching Between Local and Supabase Databases

You can easily switch between your local PostgreSQL database and Supabase:

```bash
# Switch to local PostgreSQL
npm run db:local

# Switch to Supabase
npm run db:supabase

# Check current database configuration
npm run db:status
```

## Troubleshooting

### Connection Issues

If you're having trouble connecting to Supabase:

1. Verify your credentials in `supabase/config.json`
2. Ensure your IP address is allowed in Supabase's network restrictions
3. Check that your database password is correct

### Schema Sync Issues

If you're having issues with schema synchronization:

1. Try running `npm run supabase:pull` to get the latest schema
2. Check for any conflicts between your local schema and the remote schema
3. Consider using `prisma db push --force-reset` if you need to reset the schema (caution: this will delete all data)

## Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Prisma Documentation](https://www.prisma.io/docs)
- [Prisma with Supabase Guide](https://supabase.com/docs/guides/integrations/prisma)
