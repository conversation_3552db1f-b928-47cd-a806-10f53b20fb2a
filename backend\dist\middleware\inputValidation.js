import DOMPurify from 'dompurify';
import { <PERSON><PERSON><PERSON> } from 'jsdom';
import { z } from 'zod';
/**
 * PHASE 1 SECURITY HARDENING: Enhanced Input Validation & Sanitization
 *
 * This middleware provides comprehensive input validation and sanitization
 * to prevent XSS attacks, SQL injection, and other input-based vulnerabilities.
 */
// Initialize DOMPurify with JSDOM for server-side usage
const window = new JSDOM('').window;
const purify = DOMPurify(window);
// Configure DOMPurify for strict sanitization
purify.setConfig({
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true,
    RETURN_DOM: false,
    RETURN_DOM_FRAGMENT: false,
});
// Fields that may contain HTML content and need DOMPurify sanitization
const HTML_FIELDS = [
    'description',
    'notes',
    'comments',
    'name',
    'title',
    'content',
    'message',
    'reason',
    'details',
];
// Fields that should be treated as plain text only
const TEXT_ONLY_FIELDS = [
    'email',
    'phone',
    'license',
    'vin',
    'registration',
    'id',
    'code',
];
// Maximum lengths for different field types
const FIELD_LIMITS = {
    email: 254,
    phone: 20,
    name: 100,
    title: 200,
    description: 5000,
    notes: 10000,
    comments: 2000,
    license: 50,
    vin: 17,
    registration: 20,
    default: 1000,
};
/**
 * Sanitizes a string value based on field type and options
 */
const sanitizeValue = (value, fieldName, options = {}) => {
    if (typeof value !== 'string') {
        return String(value || '').trim();
    }
    let sanitized = value.trim();
    // Apply length limits
    const maxLength = options.maxLength ||
        FIELD_LIMITS[fieldName] ||
        FIELD_LIMITS.default;
    if (sanitized.length > maxLength) {
        sanitized = sanitized.slice(0, maxLength);
    }
    // Apply HTML sanitization for specific fields
    if (HTML_FIELDS.includes(fieldName.toLowerCase()) || options.sanitizeHtml) {
        sanitized = purify.sanitize(sanitized);
    }
    else if (TEXT_ONLY_FIELDS.includes(fieldName.toLowerCase())) {
        // Strip all HTML tags for text-only fields
        sanitized = sanitized.replace(/<[^>]*>/g, '');
    }
    // Basic XSS prevention for all fields
    sanitized = sanitized
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
        .replace(/data:text\/html/gi, '');
    // SQL injection prevention
    sanitized = sanitized
        .replace(/['";]/g, '')
        .replace(/--/g, '')
        .replace(/\/\*/g, '')
        .replace(/\*\//g, '');
    return sanitized;
};
/**
 * Recursively sanitizes an object's properties
 */
const sanitizeObject = (obj, parentKey = '') => {
    if (obj === null || obj === undefined) {
        return obj;
    }
    if (typeof obj === 'string') {
        return sanitizeValue(obj, parentKey);
    }
    if (typeof obj === 'number' || typeof obj === 'boolean') {
        return obj;
    }
    if (Array.isArray(obj)) {
        // Limit array size to prevent DoS attacks
        return obj
            .slice(0, 1000)
            .map((item, index) => sanitizeObject(item, `${parentKey}[${index}]`));
    }
    if (typeof obj === 'object') {
        const sanitized = {};
        // Limit object properties to prevent DoS attacks
        const entries = Object.entries(obj).slice(0, 100);
        for (const [key, value] of entries) {
            // Sanitize the key itself
            const sanitizedKey = sanitizeValue(key, 'key', { maxLength: 50 });
            const fullKey = parentKey ? `${parentKey}.${sanitizedKey}` : sanitizedKey;
            sanitized[sanitizedKey] = sanitizeObject(value, fullKey);
        }
        return sanitized;
    }
    return obj;
};
/**
 * Validates request data against a Zod schema
 */
export const validateRequest = (schema) => {
    return (req, res, next) => {
        try {
            // First, sanitize the input data
            const sanitizedBody = sanitizeObject(req.body, 'body');
            const sanitizedParams = sanitizeObject(req.params, 'params');
            const sanitizedQuery = sanitizeObject(req.query, 'query');
            // Then validate with Zod schema
            const validatedData = schema.parse({
                body: sanitizedBody,
                params: sanitizedParams,
                query: sanitizedQuery,
            });
            // Attach validated and sanitized data to request
            req.validatedData = validatedData;
            req.body = validatedData.body;
            req.params = validatedData.params;
            req.query = validatedData.query;
            next();
        }
        catch (error) {
            if (error instanceof z.ZodError) {
                res.status(400).json({
                    error: 'Validation failed',
                    code: 'VALIDATION_ERROR',
                    details: error.errors.map((err) => ({
                        field: err.path.join('.'),
                        message: err.message,
                        code: err.code,
                    })),
                });
            }
            else {
                console.error('Input validation error:', error);
                res.status(500).json({
                    error: 'Input validation service unavailable',
                    code: 'VALIDATION_SERVICE_ERROR',
                });
            }
        }
    };
};
/**
 * Basic input sanitization middleware (without schema validation)
 */
export const sanitizeInput = (req, res, next) => {
    try {
        // Sanitize all input data
        req.body = sanitizeObject(req.body, 'body');
        req.params = sanitizeObject(req.params, 'params');
        req.query = sanitizeObject(req.query, 'query');
        next();
    }
    catch (error) {
        console.error('Input sanitization error:', error);
        res.status(500).json({
            error: 'Input sanitization service unavailable',
            code: 'SANITIZATION_ERROR',
        });
    }
};
/**
 * Rate limiting helper for input validation
 */
export const validateInputRate = (req) => {
    // Simple rate limiting based on request size
    const bodySize = JSON.stringify(req.body || {}).length;
    const paramsSize = JSON.stringify(req.params || {}).length;
    const querySize = JSON.stringify(req.query || {}).length;
    const totalSize = bodySize + paramsSize + querySize;
    // Limit total request size to 1MB
    return totalSize <= 1024 * 1024;
};
/**
 * Security headers for input validation responses
 */
export const addValidationSecurityHeaders = (res) => {
    res.setHeader('X-Input-Validation', 'PHASE-1-HARDENED');
    res.setHeader('X-Sanitization-Level', 'HIGH');
    res.setHeader('X-XSS-Protection', 'DOMPURIFY-ENABLED');
};
export default {
    validateRequest,
    sanitizeInput,
    sanitizeValue,
    sanitizeObject,
    validateInputRate,
    addValidationSecurityHeaders,
};
//# sourceMappingURL=inputValidation.js.map