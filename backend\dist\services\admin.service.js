/**
 * Admin Service
 *
 * This module provides services for admin functionality, including:
 * - Health status
 * - Performance metrics
 * - Error logs
 */
import { prisma, testDatabaseConnections, getDatabaseConfig, } from './database.service.js';
import logger from '../utils/logger.js';
import fs from 'fs';
import path from 'path';
// Track application start time for uptime calculation
const startTime = Date.now();
/**
 * Get system health status
 * @returns Health status information
 */
export const getHealthStatus = async () => {
    try {
        // Get database configuration
        const config = getDatabaseConfig();
        // Test database connections
        const connectionResults = await testDatabaseConnections();
        // Determine overall health status
        const isHealthy = connectionResults.prisma;
        // Calculate uptime in seconds
        const uptime = Math.floor((Date.now() - startTime) / 1000);
        // Prepare response
        const healthResponse = {
            status: isHealthy ? 'UP' : 'DOWN',
            message: isHealthy
                ? 'Backend service is healthy'
                : 'Backend service is unhealthy',
            components: {
                database: {
                    status: connectionResults.prisma ? 'UP' : 'DOWN',
                    type: 'PostgreSQL via Prisma',
                    url: config.databaseUrl.replace(/\/\/.*?@/, '//****@'), // Hide password
                    error: connectionResults.details?.prisma?.error || null,
                },
            },
            config: {
                useSupabase: config.useSupabase,
                supabaseConfigured: !!config.supabaseUrl && !!config.supabaseKey,
                connectionMode: config.databaseUrl.includes(':6543/')
                    ? 'transaction'
                    : 'session',
            },
            timestamp: new Date().toISOString(),
            version: process.env.npm_package_version || '1.0.0',
            uptime,
        };
        // Add Supabase status if configured
        if (config.useSupabase) {
            healthResponse.components.supabase = {
                status: connectionResults.supabase ? 'UP' : 'DOWN',
                url: config.supabaseUrl
                    ? new URL(config.supabaseUrl).hostname
                    : 'not configured',
                error: connectionResults.details?.supabase?.error || null,
            };
        }
        return healthResponse;
    }
    catch (error) {
        logger.error('Error getting health status:', error);
        throw error;
    }
};
/**
 * Get database performance metrics
 * @returns Performance metrics
 */
export const getPerformanceMetrics = async () => {
    try {
        // Query for cache hit rates
        const cacheHitRateQuery = `
      SELECT
        sum(heap_blks_read) as heap_read,
        sum(heap_blks_hit) as heap_hit,
        sum(idx_blks_read) as idx_read,
        sum(idx_blks_hit) as idx_hit
      FROM pg_statio_user_tables;
    `;
        // Query for connection count
        const connectionCountQuery = `
      SELECT count(*) as connection_count
      FROM pg_stat_activity
      WHERE datname = current_database();
    `;
        // Query for active queries
        const activeQueriesQuery = `
      SELECT count(*) as active_queries
      FROM pg_stat_activity
      WHERE datname = current_database()
      AND state = 'active'
      AND pid <> pg_backend_pid();
    `;
        // Query for average query time
        const avgQueryTimeQuery = `
      SELECT avg(extract(epoch from now() - query_start) * 1000) as avg_time
      FROM pg_stat_activity
      WHERE datname = current_database()
      AND state = 'active'
      AND pid <> pg_backend_pid();
    `;
        // Execute queries with proper type annotations
        const [cacheHitRateArray, connectionCountArray, activeQueriesArray, avgQueryTimeArray,] = await Promise.all([
            prisma.$queryRawUnsafe(cacheHitRateQuery),
            prisma.$queryRawUnsafe(connectionCountQuery),
            prisma.$queryRawUnsafe(activeQueriesQuery),
            prisma.$queryRawUnsafe(avgQueryTimeQuery),
        ]);
        // Calculate cache hit rates with proper type safety
        const cacheHitRateResult = cacheHitRateArray[0];
        const heapRead = parseInt(cacheHitRateResult?.heap_read || '0') || 0;
        const heapHit = parseInt(cacheHitRateResult?.heap_hit || '0') || 0;
        const idxRead = parseInt(cacheHitRateResult?.idx_read || '0') || 0;
        const idxHit = parseInt(cacheHitRateResult?.idx_hit || '0') || 0;
        const tableHitRate = heapRead + heapHit > 0 ? (heapHit / (heapRead + heapHit)) * 100 : 100;
        const indexHitRate = idxRead + idxHit > 0 ? (idxHit / (idxRead + idxHit)) * 100 : 100;
        // Get connection count with proper type safety
        const connectionCountResult = connectionCountArray[0];
        const connectionCount = parseInt(connectionCountResult?.connection_count || '0') || 0;
        // Get active queries with proper type safety
        const activeQueriesResult = activeQueriesArray[0];
        const activeQueries = parseInt(activeQueriesResult?.active_queries || '0') || 0;
        // Get average query time with proper type safety
        const avgQueryTimeResult = avgQueryTimeArray[0];
        const avgQueryTime = parseFloat(avgQueryTimeResult?.avg_time || '0') || 0;
        // Format results
        return {
            cacheHitRate: {
                indexHitRate,
                tableHitRate,
            },
            connectionCount,
            activeQueries,
            avgQueryTime,
            timestamp: new Date().toISOString(),
        };
    }
    catch (error) {
        logger.error('Error getting performance metrics:', error);
        throw error;
    }
};
/**
 * Get error logs with pagination and filtering
 * @param page Page number (1-based)
 * @param limit Number of items per page
 * @param level Optional log level filter
 * @returns Paginated error logs
 */
export const getErrorLogs = async (page = 1, limit = 10, level) => {
    try {
        const logsDir = path.join(process.cwd(), 'logs');
        const errorLogPath = path.join(logsDir, 'error.log');
        const combinedLogPath = path.join(logsDir, 'combined.log');
        // Check if log files exist
        const errorLogExists = fs.existsSync(errorLogPath);
        const combinedLogExists = fs.existsSync(combinedLogPath);
        // Read log files
        let logs = [];
        if (errorLogExists || combinedLogExists) {
            // Determine which file to read based on level
            const filePath = level === 'ERROR'
                ? errorLogPath
                : combinedLogExists
                    ? combinedLogPath
                    : errorLogPath;
            if (fs.existsSync(filePath)) {
                const fileContent = fs.readFileSync(filePath, 'utf8');
                const logLines = fileContent.split('\n').filter((line) => line.trim());
                // Parse log lines into structured objects
                logs = logLines.map((line, index) => {
                    try {
                        const logObject = JSON.parse(line);
                        return {
                            id: `log-${index}`,
                            timestamp: logObject.timestamp || new Date().toISOString(),
                            level: logObject.level?.toUpperCase() || 'INFO',
                            message: logObject.message || 'No message',
                            details: {
                                ...logObject,
                                message: undefined,
                                timestamp: undefined,
                                level: undefined,
                            },
                            source: logObject.service || 'system',
                        };
                    }
                    catch (e) {
                        // If line can't be parsed as JSON, create a basic entry
                        return {
                            id: `log-${index}`,
                            timestamp: new Date().toISOString(),
                            level: 'ERROR',
                            message: line,
                            source: 'system',
                        };
                    }
                });
                // Filter by level if specified
                if (level) {
                    logs = logs.filter((log) => log.level === level);
                }
            }
        }
        // Sort logs by timestamp (newest first)
        logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
        // Calculate pagination
        const total = logs.length;
        const totalPages = Math.ceil(total / limit);
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedLogs = logs.slice(startIndex, endIndex);
        // Prepare pagination metadata
        const pagination = {
            page,
            limit,
            total,
            totalPages,
        };
        return {
            data: paginatedLogs,
            pagination,
        };
    }
    catch (error) {
        logger.error('Error getting error logs:', error);
        throw error;
    }
};
//# sourceMappingURL=admin.service.js.map