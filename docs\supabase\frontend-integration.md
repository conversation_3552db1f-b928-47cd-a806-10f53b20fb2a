# Supabase Frontend Integration Guide

This guide explains how to use Supabase directly from the frontend in the Car Service Tracking System application.

## Overview

While our backend is configured to use Supabase as a database provider, we've also set up direct Supabase integration in the frontend. This allows the frontend to:

1. Connect directly to Supabase for data operations
2. Bypass the backend API for certain operations
3. Utilize Supabase's real-time features
4. Implement client-side authentication if needed

## Configuration

### Environment Variables

The frontend requires the following environment variables to connect to Supabase:

```
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
```

These should be added to your `.env.local` file in the frontend directory.

### Finding Your Supabase Credentials

1. Go to your Supabase dashboard
2. Navigate to Project Settings > API
3. Copy the "Project URL" and "anon" public API key

## Usage

### Basic Supabase Client

The Supabase client is initialized in `src/lib/supabase.ts`:

```typescript
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

export default supabase;
```

You can import and use this client directly in your components:

```typescript
import supabase from '@/lib/supabase';

// Example: Fetch data
const { data, error } = await supabase.from('vehicles').select('*');
```

### Supabase Service

For more structured data access, we've created a `supabaseService` in `src/lib/supabaseService.ts`. This service provides methods for common operations:

```typescript
import supabaseService from '@/lib/supabaseService';

// Fetch vehicles
const { data, error } = await supabaseService.getVehicles();

// Get vehicle by ID
const { data, error } = await supabaseService.getVehicleById(1);

// Create vehicle
const { data, error } = await supabaseService.createVehicle({
  make: 'Toyota',
  model: 'Corolla',
  year: 2020,
});

// Update vehicle
const { data, error } = await supabaseService.updateVehicle(1, {
  year: 2021,
});

// Delete vehicle
const { data, error } = await supabaseService.deleteVehicle(1);
```

## Diagnostics

We've created a diagnostics page at `/supabase-diagnostics` to help troubleshoot Supabase connectivity issues. This page:

1. Checks if Supabase is properly configured
2. Tests the connection to Supabase
3. Attempts to fetch data from different tables
4. Provides troubleshooting guidance

## Troubleshooting

### CORS Issues

If you're seeing CORS errors, make sure your Supabase project has the correct CORS configuration:

1. Go to your Supabase dashboard
2. Navigate to Project Settings > API
3. Under "API Settings", add your frontend URL to the "Additional Allowed Origins" field

For local development, add `http://localhost:3000`.
For production, add your production domain.

### Row Level Security (RLS) Policies

By default, Supabase tables have RLS enabled with no policies, which means no access. To allow access:

1. Go to your Supabase dashboard
2. Navigate to Table Editor > Select your table > Policies
3. Add appropriate policies for the "anon" role

Example policy to allow reading all vehicles:

```sql
CREATE POLICY "Allow public read access to vehicles"
ON vehicles
FOR SELECT
USING (true);
```

### Authentication Issues

If you're using Supabase Auth, ensure:

1. The correct redirect URLs are configured in Project Settings > Authentication > URL Configuration
2. Your application is handling authentication tokens correctly
3. Your RLS policies are set up to use `auth.uid()` for authenticated access

## Production Deployment

For production deployment:

1. Set the environment variables in your hosting platform
2. Ensure CORS is configured for your production domain
3. Set up appropriate RLS policies for production use

## Switching Between Backend API and Direct Supabase Access

You can choose to use either the backend API or direct Supabase access based on your needs:

- Use the backend API when you need server-side logic, validation, or processing
- Use direct Supabase access for simple CRUD operations or real-time features

Example of conditional usage:

```typescript
// Import both services
import apiService from '@/lib/apiService';
import supabaseService from '@/lib/supabaseService';
import { isSupabaseConfigured } from '@/lib/supabaseDebug';

// Function that uses either service based on configuration
async function getVehicles() {
  if (isSupabaseConfigured()) {
    // Use direct Supabase access
    return supabaseService.getVehicles();
  } else {
    // Fall back to backend API
    return apiService.getVehicles();
  }
}
```

## Real-time Subscriptions

One of the advantages of direct Supabase access is real-time subscriptions:

```typescript
import supabase from '@/lib/supabase';

// Subscribe to changes in the vehicles table
const subscription = supabase
  .channel('public:vehicles')
  .on('postgres_changes', { event: '*', schema: 'public', table: 'vehicles' }, (payload) => {
    console.log('Change received!', payload);
    // Update your UI state here
  })
  .subscribe();

// Clean up subscription when component unmounts
return () => {
  supabase.removeChannel(subscription);
};
```

## Security Considerations

When using Supabase directly from the frontend:

1. **Never** use the service role key in the frontend
2. Always use the anon key for public access
3. Rely on RLS policies for security, not client-side code
4. Be careful about what data you expose through RLS policies
5. Consider using Supabase Auth for user authentication
