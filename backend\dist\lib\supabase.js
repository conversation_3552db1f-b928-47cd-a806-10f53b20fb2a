import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
// Load environment variables from .env file
config();
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
if (!supabaseUrl || !supabaseServiceKey || !supabaseAnonKey) {
    throw new Error('Missing Supabase environment variables. Please check SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, and SUPABASE_ANON_KEY in your .env file');
}
// Service role client for backend operations (admin access)
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
        autoRefreshToken: false,
        persistSession: false,
    },
});
// Regular client for user operations (respects RLS)
export const supabase = createClient(supabaseUrl, supabaseAnonKey);
// Test connection function
export const testSupabaseConnection = async () => {
    try {
        const { data, error } = await supabaseAdmin
            .from('Employee')
            .select('count', { count: 'exact', head: true });
        if (error) {
            console.error('❌ Supabase connection test failed:', error.message);
            return false;
        }
        console.log('✅ Supabase connection successful');
        return true;
    }
    catch (error) {
        console.error('❌ Supabase connection test error:', error.message);
        return false;
    }
};
//# sourceMappingURL=supabase.js.map