#!/bin/bash

# PHASE 1 SECURITY HARDENING: Rate Limiting Testing Script
# This script tests the comprehensive rate limiting implementation

set -e

echo "=============================================="
echo "  WorkHub Rate Limiting Testing"
echo "  Phase 1 Security Hardening Verification"
echo "=============================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
BACKEND_URL="http://localhost:3001"
TEST_RESULTS_FILE="rate-limiting-test-results-$(date +%Y%m%d-%H%M%S).log"

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0
TOTAL_TESTS=0

echo "📋 Test Configuration:"
echo "  Backend URL: $BACKEND_URL"
echo "  Results File: $TEST_RESULTS_FILE"
echo ""

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_status="$3"
    local description="$4"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo -n "Testing: $test_name... "
    
    # Execute the test command and capture status code
    local actual_status
    actual_status=$(eval "$test_command" 2>/dev/null | head -1 | grep -o '[0-9]\{3\}' || echo "000")
    
    if [ "$actual_status" = "$expected_status" ]; then
        echo -e "${GREEN}PASS${NC} (Status: $actual_status)"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        echo "✅ $test_name: PASS - $description" >> "$TEST_RESULTS_FILE"
    else
        echo -e "${RED}FAIL${NC} (Expected: $expected_status, Got: $actual_status)"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        echo "❌ $test_name: FAIL - Expected $expected_status, Got $actual_status" >> "$TEST_RESULTS_FILE"
    fi
}

# Function to test rate limiting headers
test_rate_limit_headers() {
    echo "🔍 Testing Rate Limiting Headers..."
    
    local headers
    headers=$(curl -s -I "$BACKEND_URL/api/diagnostics" 2>/dev/null)
    
    # Test for Phase 1 rate limiting headers
    if echo "$headers" | grep -q "X-Rate-Limit-Policy: PHASE-1-HARDENED"; then
        echo -e "${GREEN}✅ PASS${NC} Phase 1 rate limiting headers present"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        echo -e "${RED}❌ FAIL${NC} Phase 1 rate limiting headers missing"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    # Test for rate limit strategy headers
    if echo "$headers" | grep -q "X-Rate-Limit-Strategy: MULTI-LAYER"; then
        echo -e "${GREEN}✅ PASS${NC} Multi-layer rate limiting strategy active"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        echo -e "${RED}❌ FAIL${NC} Multi-layer rate limiting strategy missing"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    # Test for standard rate limit headers
    if echo "$headers" | grep -q "RateLimit:"; then
        echo -e "${GREEN}✅ PASS${NC} Standard RateLimit headers present"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        echo -e "${RED}❌ FAIL${NC} Standard RateLimit headers missing"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    TOTAL_TESTS=$((TOTAL_TESTS + 3))
    echo ""
}

# Function to test rate limit status endpoint
test_rate_limit_status() {
    echo "📊 Testing Rate Limit Status Endpoint..."
    
    run_test "Rate limit status endpoint" \
        "curl -s -o /dev/null -w '%{http_code}' '$BACKEND_URL/api/rate-limit-status'" \
        "200" \
        "Rate limit status endpoint should be accessible"
    
    echo ""
}

# Function to test global rate limiting
test_global_rate_limiting() {
    echo "🌐 Testing Global Rate Limiting..."
    
    # Test normal request (should pass)
    run_test "Normal request" \
        "curl -s -o /dev/null -w '%{http_code}' '$BACKEND_URL/api/diagnostics'" \
        "200" \
        "Normal requests should pass rate limiting"
    
    echo ""
}

# Function to test admin rate limiting
test_admin_rate_limiting() {
    echo "🔐 Testing Admin Rate Limiting..."
    
    # Test admin endpoint (should have stricter limits)
    run_test "Admin endpoint access" \
        "curl -s -o /dev/null -w '%{http_code}' '$BACKEND_URL/api/admin/diagnostics'" \
        "401" \
        "Admin endpoints should require authentication (401 expected)"
    
    echo ""
}

# Function to test API rate limiting
test_api_rate_limiting() {
    echo "🔌 Testing API Rate Limiting..."
    
    # Test API endpoint
    run_test "API endpoint access" \
        "curl -s -o /dev/null -w '%{http_code}' '$BACKEND_URL/api/employees'" \
        "401" \
        "API endpoints should require authentication (401 expected)"
    
    echo ""
}

# Function to test rate limit backend
test_rate_limit_backend() {
    echo "💾 Testing Rate Limit Backend..."
    
    local headers
    headers=$(curl -s -I "$BACKEND_URL/api/diagnostics" 2>/dev/null)
    
    # Check if using memory or Redis backend
    if echo "$headers" | grep -q "X-Rate-Limit-Backend: MEMORY"; then
        echo -e "${GREEN}✅ PASS${NC} Memory-based rate limiting active"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    elif echo "$headers" | grep -q "X-Rate-Limit-Backend: REDIS"; then
        echo -e "${GREEN}✅ PASS${NC} Redis-based rate limiting active"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        echo -e "${RED}❌ FAIL${NC} Rate limiting backend not detected"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo ""
}

# Function to test rapid requests (basic rate limiting test)
test_rapid_requests() {
    echo "⚡ Testing Rapid Request Handling..."
    
    # Make multiple rapid requests
    local status_codes=()
    for i in {1..10}; do
        local status
        status=$(curl -s -o /dev/null -w '%{http_code}' "$BACKEND_URL/api/diagnostics" 2>/dev/null)
        status_codes+=("$status")
        sleep 0.1  # Small delay between requests
    done
    
    # Check if all requests succeeded (rate limit not triggered)
    local all_success=true
    for code in "${status_codes[@]}"; do
        if [ "$code" != "200" ]; then
            all_success=false
            break
        fi
    done
    
    if [ "$all_success" = true ]; then
        echo -e "${GREEN}✅ PASS${NC} Rapid requests handled correctly (within limits)"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    else
        echo -e "${YELLOW}⚠️ INFO${NC} Some requests were rate limited (expected behavior)"
        TESTS_PASSED=$((TESTS_PASSED + 1))
    fi
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo ""
}

# Function to test rate limit remaining count
test_rate_limit_remaining() {
    echo "📈 Testing Rate Limit Remaining Count..."
    
    local headers
    headers=$(curl -s -I "$BACKEND_URL/api/diagnostics" 2>/dev/null)
    
    # Check if remaining count is present and reasonable
    if echo "$headers" | grep -q "RateLimit:.*r=[0-9]"; then
        local remaining
        remaining=$(echo "$headers" | grep "RateLimit:" | sed -n 's/.*r=\([0-9]*\).*/\1/p')
        
        if [ "$remaining" -gt 0 ] && [ "$remaining" -le 1000 ]; then
            echo -e "${GREEN}✅ PASS${NC} Rate limit remaining count is valid ($remaining)"
            TESTS_PASSED=$((TESTS_PASSED + 1))
        else
            echo -e "${RED}❌ FAIL${NC} Rate limit remaining count is invalid ($remaining)"
            TESTS_FAILED=$((TESTS_FAILED + 1))
        fi
    else
        echo -e "${RED}❌ FAIL${NC} Rate limit remaining count not found"
        TESTS_FAILED=$((TESTS_FAILED + 1))
    fi
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo ""
}

# Main test execution
echo "🚀 Starting Rate Limiting Tests..."
echo ""

# Initialize results file
echo "WorkHub Rate Limiting Test Results - $(date)" > "$TEST_RESULTS_FILE"
echo "=================================================" >> "$TEST_RESULTS_FILE"
echo "" >> "$TEST_RESULTS_FILE"

# Run all tests
test_rate_limit_headers
test_rate_limit_status
test_global_rate_limiting
test_admin_rate_limiting
test_api_rate_limiting
test_rate_limit_backend
test_rapid_requests
test_rate_limit_remaining

# Summary
echo "=============================================="
echo "  🛡️ RATE LIMITING TEST RESULTS"
echo "=============================================="
echo ""
echo "📊 Test Summary:"
echo "  • Total Tests: $TOTAL_TESTS"
echo "  • Tests Passed: $TESTS_PASSED"
echo "  • Tests Failed: $TESTS_FAILED"
echo "  • Success Rate: $(( (TESTS_PASSED * 100) / TOTAL_TESTS ))%"
echo ""

# Write summary to results file
echo "" >> "$TEST_RESULTS_FILE"
echo "SUMMARY:" >> "$TEST_RESULTS_FILE"
echo "Total Tests: $TOTAL_TESTS" >> "$TEST_RESULTS_FILE"
echo "Tests Passed: $TESTS_PASSED" >> "$TEST_RESULTS_FILE"
echo "Tests Failed: $TESTS_FAILED" >> "$TEST_RESULTS_FILE"
echo "Success Rate: $(( (TESTS_PASSED * 100) / TOTAL_TESTS ))%" >> "$TEST_RESULTS_FILE"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "${GREEN}🎉 ALL RATE LIMITING TESTS PASSED!${NC}"
    echo -e "${GREEN}✅ Rate limiting protection working correctly${NC}"
    echo ""
    echo "🛡️ Security Features Verified:"
    echo "  ✅ Multi-layer rate limiting active"
    echo "  ✅ Phase 1 hardened rate limiting"
    echo "  ✅ Standard rate limit headers"
    echo "  ✅ Rate limit status monitoring"
    echo "  ✅ Backend rate limiting functional"
    echo ""
    echo "📄 Detailed results saved to: $TEST_RESULTS_FILE"
    exit 0
else
    echo -e "${RED}❌ RATE LIMITING ISSUES DETECTED${NC}"
    echo -e "${YELLOW}⚠️  Please review failed tests and address issues${NC}"
    echo ""
    echo "📄 Detailed results saved to: $TEST_RESULTS_FILE"
    exit 1
fi
