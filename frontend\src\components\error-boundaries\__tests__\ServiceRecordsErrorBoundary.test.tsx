import {render, screen, fireEvent} from '@testing-library/react';
import ServiceRecordsErrorBoundary from '../ServiceRecordsErrorBoundary';
import React from 'react';

// Create a component that throws an error
const ErrorComponent = ({shouldThrow = true}: {shouldThrow?: boolean}) => {
	if (shouldThrow) {
		throw new Error('Test error');
	}
	return <div>No error</div>;
};

// Mock console.error to prevent test output noise
const originalConsoleError = console.error;
beforeEach(() => {
	console.error = jest.fn();
});

afterEach(() => {
	console.error = originalConsoleError;
});

describe('ServiceRecordsErrorBoundary', () => {
	it('should render children when there is no error', () => {
		// Setup
		render(
			<ServiceRecordsErrorBoundary>
				<div>Test content</div>
			</ServiceRecordsErrorBoundary>
		);

		// Verify
		expect(screen.getByText('Test content')).toBeInTheDocument();
	});

	it('should render error UI when a child component throws', () => {
		// We need to spy on console.error and mock it to avoid test output noise
		// from React's error boundary warnings
		const spy = jest.spyOn(console, 'error');
		spy.mockImplementation(() => {});

		// Setup - render a component that throws
		render(
			<ServiceRecordsErrorBoundary>
				<ErrorComponent />
			</ServiceRecordsErrorBoundary>
		);

		// Verify
		expect(
			screen.getByText('Error Loading Service Records')
		).toBeInTheDocument();
		expect(screen.getByText('Test error')).toBeInTheDocument();
		expect(screen.getByText('Try Again')).toBeInTheDocument();

		// Cleanup
		spy.mockRestore();
	});

	it('should render custom fallback if provided', () => {
		// Setup
		render(
			<ServiceRecordsErrorBoundary fallback={<div>Custom fallback</div>}>
				<ErrorComponent />
			</ServiceRecordsErrorBoundary>
		);

		// Verify
		expect(screen.getByText('Custom fallback')).toBeInTheDocument();
	});

	it('should reset error state when retry button is clicked', () => {
		// We need a component that can toggle its error state
		const TestComponent = () => {
			const [shouldThrow, setShouldThrow] = React.useState(true);

			// After the error boundary catches the error and renders its UI,
			// we'll simulate clicking the retry button, which should reset the error state
			// and re-render the children. We'll use this effect to stop throwing on retry.
			React.useEffect(() => {
				// This will run when the component mounts after retry
				if (shouldThrow) {
					setShouldThrow(false);
				}
			}, [shouldThrow]);

			if (shouldThrow) {
				throw new Error('Test error');
			}

			return <div>Recovered from error</div>;
		};

		// Setup
		render(
			<ServiceRecordsErrorBoundary>
				<TestComponent />
			</ServiceRecordsErrorBoundary>
		);

		// Verify initial error state
		expect(
			screen.getByText('Error Loading Service Records')
		).toBeInTheDocument();

		// Click retry button
		fireEvent.click(screen.getByText('Try Again'));

		// Verify recovery
		expect(screen.getByText('Recovered from error')).toBeInTheDocument();
	});
});
