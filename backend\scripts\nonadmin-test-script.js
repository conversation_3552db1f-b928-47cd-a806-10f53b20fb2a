/**
 * NON-ADMIN User Test Script for Browser Console
 * 
 * This script tests the Hybrid RBAC system with a NON-ADMIN user
 * to ensure proper access control and JWT custom claims functionality.
 * 
 * USAGE: Copy and paste this entire script into the browser console
 * after logging <NAME_EMAIL>
 */

console.log('🔐 HYBRID RBAC Test - NON-ADMIN User (CRITICAL SECURITY TEST)');
console.log('==============================================================\n');

const BASE_API_URL = 'http://localhost:3001';

// Get the JWT token from the current session
let authToken = null;
let userRole = null;
let hasCustomClaims = false;

try {
  const authData = localStorage.getItem('sb-abylqjnpaegeqwktcukn-auth-token');
  if (authData) {
    authToken = JSON.parse(authData).access_token;
    
    // Decode JWT to verify role and custom claims
    const payload = JSON.parse(atob(authToken.split('.')[1]));
    
    console.log('✅ JWT Token found for NON-ADMIN testing');
    console.log('🔍 Token payload analysis:');
    
    // Check for custom claims (new Hybrid RBAC system)
    if (payload.custom_claims && payload.custom_claims.user_role) {
      hasCustomClaims = true;
      userRole = payload.custom_claims.user_role;
      console.log('   ✅ Custom claims found (Hybrid RBAC active)');
      console.log(`   📋 Role from custom claims: ${userRole}`);
      console.log(`   📋 Is active: ${payload.custom_claims.is_active}`);
      console.log(`   📋 Employee ID: ${payload.custom_claims.employee_id || 'none'}`);
    } else {
      // Fallback to user metadata (old system)
      userRole = payload.user_metadata?.role || 'USER';
      console.log('   ⚠️  No custom claims found (using fallback)');
      console.log(`   📋 Role from user_metadata: ${userRole}`);
    }
    
    console.log(`\n🎯 Testing with role: ${userRole}`);
    console.log(`🎯 Custom claims active: ${hasCustomClaims ? 'YES' : 'NO'}`);
    
  } else {
    console.error('❌ No auth token found. Please log in first.');
  }
} catch (error) {
  console.error('❌ Error getting auth token:', error);
}

if (!authToken) {
  console.error('❌ Cannot proceed without authentication token.');
  console.log('\n🔧 Troubleshooting:');
  console.log('1. Ensure you are logged <NAME_EMAIL>');
  console.log('2. Check that localStorage contains auth token');
  console.log('3. Refresh the page and try again');
} else {
  
  // Test 1: Admin Diagnostics (Should be DENIED for NON-ADMIN)
  console.log('\n=== Test 1: Admin Diagnostics (Should be DENIED) ===');
  fetch(`${BASE_API_URL}/api/admin/diagnostics`, {
    headers: {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    }
  })
  .then(response => {
    console.log(`📊 Admin diagnostics status: ${response.status}`);
    if (response.status === 403) {
      console.log('✅ SUCCESS: NON-ADMIN correctly denied admin endpoints');
      console.log('   🔒 RBAC working: USER role blocked from admin access');
    } else if (response.status === 200) {
      console.log('❌ CRITICAL FAIL: NON-ADMIN incorrectly allowed admin access');
      console.log('   🚨 SECURITY ISSUE: Role-based access control not working!');
    } else if (response.status === 401) {
      console.log('❌ FAIL: Authentication failed (invalid token)');
      console.log('   🔧 Check: JWT token validity and auth hook configuration');
    } else {
      console.log(`⚠️  Unexpected status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    console.log('📄 Admin diagnostics response:', data);
    if (data.error && data.code === 'NO_ROLE_CLAIMS') {
      console.log('🔧 HINT: Auth hook may not be injecting custom claims properly');
    }
  })
  .catch(err => console.error('❌ Admin diagnostics error:', err));

  // Test 2: Create Employee (Should be DENIED for NON-ADMIN)
  setTimeout(() => {
    console.log('\n=== Test 2: Create Employee (Should be DENIED) ===');
    fetch(`${BASE_API_URL}/api/employees`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: 'Test Employee NON-ADMIN',
        role: 'office_staff',
        employeeId: 'EMP-NONADMIN-' + Date.now(),
        contactInfo: '<EMAIL>',
        contactEmail: '<EMAIL>',
        position: 'Test Position',
        department: 'IT',
        hireDate: new Date().toISOString(),
        status: 'Active'
      })
    })
    .then(response => {
      console.log(`📊 Create employee status: ${response.status}`);
      if (response.status === 403) {
        console.log('✅ SUCCESS: NON-ADMIN correctly denied employee creation');
        console.log('   🔒 RBAC working: USER role blocked from creating employees');
      } else if (response.status === 201) {
        console.log('❌ CRITICAL FAIL: NON-ADMIN incorrectly allowed to create employees');
        console.log('   🚨 SECURITY ISSUE: Role-based access control not working!');
      } else if (response.status === 401) {
        console.log('❌ FAIL: Authentication failed (invalid token)');
      } else {
        console.log(`⚠️  Unexpected status: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      console.log('📄 Create employee response:', data);
    })
    .catch(err => console.error('❌ Create employee error:', err));
  }, 1000);

  // Test 3: Get Employees (Should be ALLOWED but possibly filtered)
  setTimeout(() => {
    console.log('\n=== Test 3: Get Employees (Should be ALLOWED, check data filtering) ===');
    fetch(`${BASE_API_URL}/api/employees`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    })
    .then(response => {
      console.log(`📊 Get employees status: ${response.status}`);
      if (response.status === 200) {
        console.log('✅ SUCCESS: NON-ADMIN can view employees');
      } else if (response.status === 403) {
        console.log('⚠️  INFO: NON-ADMIN denied employee viewing (depends on RLS policy)');
      } else if (response.status === 401) {
        console.log('❌ FAIL: Authentication failed');
      }
      return response.json();
    })
    .then(data => {
      if (Array.isArray(data)) {
        console.log(`📄 Get employees result: ${data.length} employees visible to NON-ADMIN`);
        console.log('🔍 RLS Check: Compare this count to ADMIN count (8) to verify data filtering');
        if (data.length > 0) {
          console.log('📋 Sample employee data:', data[0]);
        }
      } else {
        console.log('📄 Get employees result:', data);
      }
    })
    .catch(err => console.error('❌ Get employees error:', err));
  }, 2000);

  // Test 4: JWT Custom Claims Verification
  setTimeout(() => {
    console.log('\n=== Test 4: JWT Custom Claims Verification ===');
    if (hasCustomClaims) {
      console.log('✅ SUCCESS: JWT contains custom claims from Hybrid RBAC system');
      console.log('   🎯 Auth hook is working correctly');
      console.log('   🎯 Role is being injected from user_profiles table');
    } else {
      console.log('❌ FAIL: JWT missing custom claims');
      console.log('   🔧 Check: Auth hook configuration in Supabase Dashboard');
      console.log('   🔧 Check: custom_access_token_hook function exists');
      console.log('   🔧 Action: User may need to sign out and sign in again');
    }
  }, 3000);

  // Test 5: Summary Report
  setTimeout(() => {
    console.log('\n🎯 HYBRID RBAC TEST SUMMARY');
    console.log('===========================');
    console.log(`👤 User: <EMAIL>`);
    console.log(`🔑 Role: ${userRole}`);
    console.log(`🔧 Custom Claims: ${hasCustomClaims ? 'Active' : 'Missing'}`);
    console.log('\n📋 Expected Results:');
    console.log('• Test 1: ❌ 403 Forbidden - Admin diagnostics denied');
    console.log('• Test 2: ❌ 403 Forbidden - Employee creation denied');
    console.log('• Test 3: ✅ 200 OK - Employee viewing allowed (data may be filtered)');
    console.log('• Test 4: ✅ Custom claims present in JWT token');
    console.log('\n🚨 CRITICAL: If any admin endpoints return 200 OK, there is a security vulnerability!');
    
    if (!hasCustomClaims) {
      console.log('\n⚠️  IMPORTANT: Custom claims missing - Hybrid RBAC not fully active');
      console.log('🔧 Next steps:');
      console.log('1. Verify auth hook is configured in Supabase Dashboard');
      console.log('2. Execute helper functions in Supabase SQL Editor');
      console.log('3. Sign out and sign in again to get new JWT with custom claims');
    }
  }, 4000);
}

console.log('\n⏳ Running tests... Results will appear above in 4 seconds...');
