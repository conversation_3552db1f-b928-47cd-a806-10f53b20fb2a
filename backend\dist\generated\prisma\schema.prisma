// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Vehicle {
  id              Int      @id @default(autoincrement())
  make            String
  model           String
  year            Int
  vin             String   @unique
  licensePlate    String
  ownerName       String
  ownerContact    String
  color           String?
  initialOdometer Int?
  imageUrl        String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  serviceRecords ServiceRecord[]
  tasks          Task[] // Tasks associated with this vehicle
  assignedDriver Employee?       @relation("DriverVehicle") // A vehicle can be assigned to one driver
}

model Employee {
  id                 Int                   @id @default(autoincrement())
  name               String
  fullName           String? // Added for frontend consistency, can be derived or same as name
  role               EmployeeRole
  employeeId         String                @unique // Business key
  contactInfo        String
  contactEmail       String? // Added for frontend consistency, can be part of contactInfo
  contactPhone       String? // Added for frontend consistency
  contactMobile      String? // Added for frontend consistency
  position           String?
  department         String?
  hireDate           DateTime?
  status             EmployeeStatus?
  availability       DriverAvailability? // For drivers
  currentLocation    String? // For drivers
  workingHours       String? // For drivers
  assignedVehicleId  Int?                  @unique
  assignedVehicle    Vehicle?              @relation("DriverVehicle", fields: [assignedVehicleId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  skills             String[]              @default([])
  shiftSchedule      String?
  generalAssignments String[]              @default([])
  notes              String?
  profileImageUrl    String?
  createdAt          DateTime              @default(now())
  updatedAt          DateTime              @updatedAt
  statusHistory      EmployeeStatusEntry[]
  serviceRecords     ServiceRecord[] // Services performed by this employee
  assignedTasks      Task[]                @relation("EmployeeTasks")
}

model EmployeeStatusEntry {
  id         String         @id @default(uuid())
  employeeId Int
  employee   Employee       @relation(fields: [employeeId], references: [id], onDelete: Cascade)
  status     EmployeeStatus
  changedAt  DateTime       @default(now())
  reason     String?
}

model ServiceRecord {
  id               String    @id @default(uuid())
  vehicleId        Int
  vehicle          Vehicle   @relation(fields: [vehicleId], references: [id], onDelete: Cascade)
  employeeId       Int?
  employee         Employee? @relation(fields: [employeeId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  date             DateTime
  odometer         Int
  servicePerformed String[]
  notes            String?
  cost             Decimal?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
}

model Delegation {
  id                     String                  @id @default(uuid())
  eventName              String
  location               String
  durationFrom           DateTime
  durationTo             DateTime
  invitationFrom         String?
  invitationTo           String?
  delegates              Delegate[]
  flightArrivalId        String?                 @unique
  flightArrivalDetails   FlightDetails?          @relation("DelegationArrivalFlight", fields: [flightArrivalId], references: [id], onDelete: SetNull)
  flightDepartureId      String?                 @unique
  flightDepartureDetails FlightDetails?          @relation("DelegationDepartureFlight", fields: [flightDepartureId], references: [id], onDelete: SetNull)
  status                 DelegationStatus
  statusHistory          DelegationStatusEntry[]
  notes                  String?
  imageUrl               String?
  createdAt              DateTime                @default(now())
  updatedAt              DateTime                @updatedAt
}

model Delegate {
  id           String     @id @default(uuid())
  delegationId String
  delegation   Delegation @relation(fields: [delegationId], references: [id], onDelete: Cascade)
  name         String
  title        String
  notes        String?
}

model DelegationStatusEntry {
  id           String           @id @default(uuid())
  delegationId String
  delegation   Delegation       @relation(fields: [delegationId], references: [id], onDelete: Cascade)
  status       DelegationStatus
  changedAt    DateTime         @default(now())
  reason       String?
}

model FlightDetails {
  id                    String      @id @default(uuid())
  flightNumber          String
  dateTime              DateTime
  airport               String
  terminal              String?
  notes                 String?
  arrivalDelegationId   String?     @unique // Foreign key for one-to-one from Delegation (arrival)
  departureDelegationId String?     @unique // Foreign key for one-to-one from Delegation (departure)
  arrivalDelegation     Delegation? @relation("DelegationArrivalFlight")
  departureDelegation   Delegation? @relation("DelegationDepartureFlight")
}

model Task {
  id                  String            @id @default(uuid())
  description         String
  location            String
  dateTime            DateTime // Start date & time
  estimatedDuration   Int // in minutes
  requiredSkills      String[]          @default([])
  priority            TaskPriority
  deadline            DateTime?
  status              TaskStatus
  notes               String?
  vehicleId           Int?
  vehicle             Vehicle?          @relation(fields: [vehicleId], references: [id], onDelete: SetNull, onUpdate: Cascade)
  assignedEmployees   Employee[]        @relation("EmployeeTasks")
  subTasks            SubTask[]
  statusHistory       TaskStatusEntry[]
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt
  assignedEmployeeIds String[]          @default([]) // Stores Employee string IDs, to be managed by application logic
}

model SubTask {
  id        String  @id @default(uuid())
  taskId    String
  task      Task    @relation(fields: [taskId], references: [id], onDelete: Cascade)
  title     String
  completed Boolean @default(false)
}

model TaskStatusEntry {
  id        String     @id @default(uuid())
  taskId    String
  task      Task       @relation(fields: [taskId], references: [id], onDelete: Cascade)
  status    TaskStatus
  changedAt DateTime   @default(now())
  reason    String?
}

enum EmployeeRole {
  driver
  mechanic
  administrator
  office_staff
  manager
  service_advisor
  technician
  other
}

enum EmployeeStatus {
  Active
  On_Leave // Prisma enum values cannot contain spaces
  Terminated
  Inactive // Added for consistency with frontend schema
}

enum DriverAvailability {
  On_Shift
  Off_Shift
  On_Break
  Busy
}

enum DelegationStatus {
  Planned
  Confirmed
  In_Progress
  Completed
  Cancelled
  No_details // Prisma enum values cannot contain spaces
}

enum TaskStatus {
  Pending
  Assigned
  In_Progress
  Completed
  Cancelled
}

enum TaskPriority {
  Low
  Medium
  High
}
