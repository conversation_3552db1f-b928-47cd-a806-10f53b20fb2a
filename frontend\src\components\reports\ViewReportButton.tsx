import React from 'react';
import Link from 'next/link';
import { FileText, ExternalLink } from 'lucide-react';
import { ActionButton } from '@/components/ui/action-button';

interface ViewReportButtonProps {
  /**
   * Direct URL to the report page (for individual item reports)
   */
  href?: string;
  
  /**
   * Handler function that returns the URL to the report page (for list reports with dynamic parameters)
   */
  getReportUrl?: () => string;
  
  /**
   * Whether this button is for a list report (affects button text)
   */
  isList?: boolean;
  
  /**
   * Additional CSS class names
   */
  className?: string;
}

/**
 * Standardized button for navigating to report pages.
 * 
 * For individual item reports (e.g., Vehicle Report, Delegation Report):
 * ```tsx
 * <ViewReportButton href={`/vehicles/${vehicle.id}/report`} />
 * ```
 * 
 * For list reports with dynamic parameters (e.g., Tasks Report, Delegations List Report):
 * ```tsx
 * <ViewReportButton 
 *   isList={true}
 *   getReportUrl={() => {
 *     const params = new URLSearchParams({ searchTerm, status });
 *     return `/tasks/report?${params}`;
 *   }}
 * />
 * ```
 */
export function ViewReportButton({
  href,
  getReportUrl,
  isList = false,
  className,
}: ViewReportButtonProps) {
  if (!href && !getReportUrl) {
    console.error('ViewReportButton requires either href or getReportUrl prop');
    return null;
  }

  const buttonText = isList ? 'View List Report' : 'View Report';
  
  // For direct links (individual item reports)
  if (href) {
    return (
      <ActionButton
        actionType='secondary'
        asChild
        icon={<FileText className='h-4 w-4' />}
        className={className}
      >
        <Link 
          href={href} 
          target='_blank'
          rel='noopener noreferrer'
        >
          {buttonText}
          <ExternalLink className='h-3 w-3 ml-1.5 inline-block' aria-hidden="true" />
          <span className="sr-only">(opens in new tab)</span>
        </Link>
      </ActionButton>
    );
  }
  
  // For dynamic links (list reports)
  const handleClick = () => {
    if (getReportUrl) {
      const reportUrl = getReportUrl();
      window.open(reportUrl, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <ActionButton
      actionType='secondary'
      onClick={handleClick}
      icon={<FileText className='h-4 w-4' />}
      className={className}
    >
      {buttonText}
      <ExternalLink className='h-3 w-3 ml-1.5 inline-block' aria-hidden="true" />
    </ActionButton>
  );
}
