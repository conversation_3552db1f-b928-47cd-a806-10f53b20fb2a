/**
 * EMERGENCY SECURITY COMPONENTS - Authentication Module
 * 
 * This module exports all authentication-related components
 * for the emergency security implementation.
 * 
 * CRITICAL: These components are part of the emergency security implementation
 */

export { LoginForm } from './LoginForm';
export { ProtectedRoute } from './ProtectedRoute';
export { UserProfile } from './UserProfile';

// Re-export auth context and hooks for convenience
export { AuthProvider, useAuthContext } from '../../contexts/AuthContext';
export { useAuth } from '../../hooks/useAuth';

// Type exports
export type { UseAuthReturn } from '../../hooks/useAuth';
