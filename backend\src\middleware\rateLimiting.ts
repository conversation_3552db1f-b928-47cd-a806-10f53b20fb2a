import {rateLimit} from 'express-rate-limit';
import {RateLimiterMemory, RateLimiterRedis} from 'rate-limiter-flexible';
import {Request, Response, NextFunction} from 'express';
import {Redis} from 'ioredis';

/**
 * PHASE 1 SECURITY HARDENING: Rate Limiting Implementation
 *
 * This module provides comprehensive rate limiting protection against:
 * - Brute force attacks
 * - DDoS attacks
 * - API abuse
 * - Resource exhaustion
 *
 * Features:
 * - Multiple rate limiting strategies
 * - Redis support for distributed systems
 * - Endpoint-specific limits
 * - Security headers
 * - Graceful degradation
 */

// Rate limiting configuration
const RATE_LIMIT_CONFIG = {
	// Global API rate limits
	global: {
		windowMs: 15 * 60 * 1000, // 15 minutes
		limit: 1000, // requests per window per IP
		message: {
			error: 'Too many requests from this IP, please try again later.',
			code: 'RATE_LIMIT_EXCEEDED',
			retryAfter: '15 minutes',
		},
	},

	// Authentication endpoints (stricter)
	auth: {
		windowMs: 15 * 60 * 1000, // 15 minutes
		limit: 10, // requests per window per IP
		message: {
			error: 'Too many authentication attempts, please try again later.',
			code: 'AUTH_RATE_LIMIT_EXCEEDED',
			retryAfter: '15 minutes',
		},
	},

	// API endpoints (moderate)
	api: {
		windowMs: 1 * 60 * 1000, // 1 minute
		limit: 100, // requests per window per IP
		message: {
			error: 'API rate limit exceeded, please slow down.',
			code: 'API_RATE_LIMIT_EXCEEDED',
			retryAfter: '1 minute',
		},
	},

	// Admin endpoints (very strict)
	admin: {
		windowMs: 5 * 60 * 1000, // 5 minutes
		limit: 20, // requests per window per IP
		message: {
			error: 'Admin endpoint rate limit exceeded.',
			code: 'ADMIN_RATE_LIMIT_EXCEEDED',
			retryAfter: '5 minutes',
		},
	},

	// File upload endpoints (strict)
	upload: {
		windowMs: 10 * 60 * 1000, // 10 minutes
		limit: 5, // requests per window per IP
		message: {
			error: 'Upload rate limit exceeded, please wait before uploading again.',
			code: 'UPLOAD_RATE_LIMIT_EXCEEDED',
			retryAfter: '10 minutes',
		},
	},
};

// Redis configuration for distributed rate limiting
let redisClient: Redis | null = null;
let rateLimiterRedis: RateLimiterRedis | null = null;

/**
 * Initialize Redis connection for distributed rate limiting
 */
const initializeRedis = (): void => {
	const redisUrl = process.env.REDIS_URL;

	if (redisUrl && process.env.NODE_ENV === 'production') {
		try {
			redisClient = new Redis(redisUrl, {
				maxRetriesPerRequest: 3,
				lazyConnect: true,
			});

			rateLimiterRedis = new RateLimiterRedis({
				storeClient: redisClient,
				keyPrefix: 'workhub_rl',
				points: 100, // Number of requests
				duration: 60, // Per 60 seconds
			});

			console.log('🔄 PHASE 1 SECURITY: Redis rate limiting initialized');
		} catch (error) {
			console.error('❌ Redis rate limiting initialization failed:', error);
			console.log('🔄 Falling back to memory-based rate limiting');
		}
	}
};

// Initialize Redis on module load
initializeRedis();

/**
 * Create rate limiter with custom configuration
 */
const createRateLimiter = (config: typeof RATE_LIMIT_CONFIG.global) => {
	return rateLimit({
		windowMs: config.windowMs,
		limit: config.limit,
		message: config.message,
		standardHeaders: 'draft-8', // Use latest standard headers
		legacyHeaders: false, // Disable legacy X-RateLimit-* headers

		// Custom key generator for better IP detection
		keyGenerator: (req: Request): string => {
			// Handle proxy scenarios
			const forwarded = req.headers['x-forwarded-for'];
			if (forwarded && typeof forwarded === 'string') {
				// Take the first IP from the chain
				const ip = forwarded.split(',')[0].trim();
				// Remove port if present (e.g., "***********:8080" -> "***********")
				return ip.replace(/:\d+[^:]*$/, '');
			}

			return req.ip || req.socket.remoteAddress || 'unknown';
		},

		// Custom handler for rate limit exceeded
		handler: (req: Request, res: Response, next: NextFunction) => {
			// Add security headers
			res.setHeader('X-Rate-Limit-Policy', 'PHASE-1-HARDENED');
			res.setHeader('X-Security-Incident', 'RATE_LIMIT_EXCEEDED');
			res.setHeader('X-Security-Timestamp', new Date().toISOString());

			// Log rate limit violation
			console.warn(
				`🚨 PHASE 1 SECURITY: Rate limit exceeded for IP ${req.ip} on ${req.path}`
			);

			// Send rate limit response
			res.status(429).json(config.message);
		},

		// Skip function for allowlisted IPs or authenticated admin users
		skip: (req: Request): boolean => {
			// Skip rate limiting for localhost in development
			if (process.env.NODE_ENV === 'development' && req.ip === '127.0.0.1') {
				return true;
			}

			// Skip for allowlisted IPs (if configured)
			const allowlist = process.env.RATE_LIMIT_ALLOWLIST?.split(',') || [];
			if (req.ip && allowlist.includes(req.ip)) {
				return true;
			}

			return false;
		},

		// Only count failed requests for auth endpoints
		requestWasSuccessful: (req: Request, res: Response): boolean => {
			// For auth endpoints, only count failed attempts
			if (req.path.includes('/auth') || req.path.includes('/login')) {
				return res.statusCode < 400;
			}

			// For other endpoints, count all requests
			return true;
		},
	});
};

/**
 * Advanced rate limiter using rate-limiter-flexible for complex scenarios
 */
const createAdvancedRateLimiter = (points: number, duration: number) => {
	const limiter =
		rateLimiterRedis ||
		new RateLimiterMemory({
			points, // Number of requests
			duration, // Per duration in seconds
		});

	return async (
		req: Request,
		res: Response,
		next: NextFunction
	): Promise<void> => {
		const key = req.ip || 'unknown';

		try {
			await limiter.consume(key);
			next();
		} catch (rateLimiterRes: any) {
			// Rate limit exceeded
			const secs = Math.round(rateLimiterRes.msBeforeNext / 1000) || 1;

			res.set({
				'Retry-After': String(secs),
				'X-RateLimit-Limit': String(points),
				'X-RateLimit-Remaining': String(rateLimiterRes.remainingPoints || 0),
				'X-RateLimit-Reset': String(
					Math.ceil((Date.now() + rateLimiterRes.msBeforeNext) / 1000)
				),
				'X-Rate-Limit-Policy': 'PHASE-1-HARDENED-ADVANCED',
			});

			console.warn(
				`🚨 PHASE 1 SECURITY: Advanced rate limit exceeded for IP ${req.ip}`
			);

			res.status(429).json({
				error: 'Rate limit exceeded',
				code: 'ADVANCED_RATE_LIMIT_EXCEEDED',
				retryAfter: `${secs} seconds`,
			});
		}
	};
};

// Pre-configured rate limiters
export const globalRateLimit = createRateLimiter(RATE_LIMIT_CONFIG.global);
export const authRateLimit = createRateLimiter(RATE_LIMIT_CONFIG.auth);
export const apiRateLimit = createRateLimiter(RATE_LIMIT_CONFIG.api);
export const adminRateLimit = createRateLimiter(RATE_LIMIT_CONFIG.admin);
export const uploadRateLimit = createRateLimiter(RATE_LIMIT_CONFIG.upload);

// Advanced rate limiters
export const strictRateLimit = createAdvancedRateLimiter(10, 60); // 10 requests per minute
export const bruteForceProtection = createAdvancedRateLimiter(5, 300); // 5 attempts per 5 minutes

/**
 * Rate limiting security headers middleware
 */
export const addRateLimitSecurityHeaders = (
	req: Request,
	res: Response,
	next: NextFunction
): void => {
	res.setHeader('X-Rate-Limit-Policy', 'PHASE-1-HARDENED');
	res.setHeader('X-Rate-Limit-Strategy', 'MULTI-LAYER');
	res.setHeader('X-Rate-Limit-Backend', rateLimiterRedis ? 'REDIS' : 'MEMORY');
	next();
};

/**
 * Rate limit status endpoint for monitoring
 */
export const getRateLimitStatus = async (
	req: Request,
	res: Response
): Promise<void> => {
	const key = req.ip || 'unknown';

	try {
		if (rateLimiterRedis) {
			const resRateLimiter = await rateLimiterRedis.get(key);
			res.json({
				status: 'active',
				backend: 'redis',
				remaining: resRateLimiter?.remainingPoints || 0,
				resetTime: resRateLimiter
					? new Date(Date.now() + resRateLimiter.msBeforeNext)
					: null,
			});
		} else {
			res.json({
				status: 'active',
				backend: 'memory',
				message: 'Memory-based rate limiting active',
			});
		}
	} catch (error) {
		res.status(500).json({
			status: 'error',
			message: 'Rate limit status unavailable',
		});
	}
};

export default {
	globalRateLimit,
	authRateLimit,
	apiRateLimit,
	adminRateLimit,
	uploadRateLimit,
	strictRateLimit,
	bruteForceProtection,
	addRateLimitSecurityHeaders,
	getRateLimitStatus,
};
