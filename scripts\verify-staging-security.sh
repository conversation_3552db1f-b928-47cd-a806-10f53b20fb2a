#!/bin/bash

# WorkHub Automated Security Verification Script
# Purpose: Comprehensive security testing of deployed staging environment
# Created: January 24, 2025
# Enhanced: January 24, 2025 - Advanced security testing with detailed reporting

# Script configuration
set -o pipefail  # Exit on pipe failures but allow error handling

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Test results tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
WARNING_TESTS=0
CRITICAL_FAILURES=0

# Configuration - Can be overridden by environment variables
BACKEND_URL="${BACKEND_URL:-http://localhost:3001}"
FRONTEND_URL="${FRONTEND_URL:-http://localhost:3000}"
CURL_TIMEOUT="${CURL_TIMEOUT:-5}"
VERBOSE="${VERBOSE:-false}"
SAVE_REPORT="${SAVE_REPORT:-true}"
REPORT_FILE="${REPORT_FILE:-security-verification-$(date +%Y%m%d-%H%M%S).log}"

# Security test configuration
MALFORMED_TOKENS=(
    "Bearer"
    "Bearer "
    "Bearer invalid"
    "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9"
    "Bearer null"
    "Bearer undefined"
    "invalid-format"
    ""
)

API_ENDPOINTS=(
    "/api/employees"
    "/api/vehicles"
    "/api/delegations"
    "/api/tasks"
    "/api/admin/diagnostics"
    "/api/admin/health"
    "/api/servicerecords"
)

CRITICAL_ENDPOINTS=(
    "/api/admin/diagnostics"
    "/api/admin/health"
    "/api/employees"
)

# Logging and reporting functions
log() {
    local message="[$(date +'%Y-%m-%d %H:%M:%S')] $1"
    echo -e "${BLUE}${message}${NC}"
    [ "$SAVE_REPORT" = "true" ] && echo "$message" >> "$REPORT_FILE"
}

success() {
    local message="[✅ PASS] $1"
    echo -e "${GREEN}${message}${NC}"
    PASSED_TESTS=$((PASSED_TESTS + 1))
    [ "$SAVE_REPORT" = "true" ] && echo "$message" >> "$REPORT_FILE"
}

fail() {
    local message="[❌ FAIL] $1"
    echo -e "${RED}${message}${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
    [ "$SAVE_REPORT" = "true" ] && echo "$message" >> "$REPORT_FILE"
}

critical_fail() {
    local message="[🚨 CRITICAL] $1"
    echo -e "${RED}${message}${NC}"
    FAILED_TESTS=$((FAILED_TESTS + 1))
    CRITICAL_FAILURES=$((CRITICAL_FAILURES + 1))
    [ "$SAVE_REPORT" = "true" ] && echo "$message" >> "$REPORT_FILE"
}

warning() {
    local message="[⚠️  WARN] $1"
    echo -e "${YELLOW}${message}${NC}"
    WARNING_TESTS=$((WARNING_TESTS + 1))
    [ "$SAVE_REPORT" = "true" ] && echo "$message" >> "$REPORT_FILE"
}

info() {
    local message="[ℹ️  INFO] $1"
    echo -e "${CYAN}${message}${NC}"
    [ "$SAVE_REPORT" = "true" ] && echo "$message" >> "$REPORT_FILE"
}

test_start() {
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    local message="Test $TOTAL_TESTS: $1"
    log "$message"
    [ "$VERBOSE" = "true" ] && info "Starting test: $1"
}

# Enhanced HTTP status check with detailed error reporting
check_http_status() {
    local url="$1"
    local expected_status="$2"
    local headers="$3"
    local description="${4:-HTTP request}"

    local start_time=$(date +%s%N)
    local actual_status
    local response_time

    if [ -n "$headers" ]; then
        actual_status=$(curl -s -o /dev/null -w "%{http_code}" --max-time $CURL_TIMEOUT -H "$headers" "$url" 2>/dev/null || echo "000")
    else
        actual_status=$(curl -s -o /dev/null -w "%{http_code}" --max-time $CURL_TIMEOUT "$url" 2>/dev/null || echo "000")
    fi

    local end_time=$(date +%s%N)
    response_time=$(( (end_time - start_time) / 1000000 )) # Convert to milliseconds

    [ "$VERBOSE" = "true" ] && info "$description: $url -> $actual_status (${response_time}ms)"

    if [ "$actual_status" = "$expected_status" ]; then
        return 0
    else
        echo "$actual_status"
        return 1
    fi
}

# Check if service is reachable
check_service_availability() {
    local url="$1"
    local service_name="$2"

    if curl -s --max-time $CURL_TIMEOUT "$url" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Initialize report file
initialize_report() {
    if [ "$SAVE_REPORT" = "true" ]; then
        cat > "$REPORT_FILE" << EOF
WorkHub Security Verification Report
Generated: $(date)
Backend URL: $BACKEND_URL
Frontend URL: $FRONTEND_URL
Timeout: ${CURL_TIMEOUT}s
Verbose: $VERBOSE

=== SECURITY VERIFICATION RESULTS ===

EOF
        info "Report will be saved to: $REPORT_FILE"
    fi
}

# Display banner and configuration
show_banner() {
    echo -e "${GREEN}"
    echo "=============================================="
    echo "  WorkHub Automated Security Verification"
    echo "  Enhanced Testing with Detailed Reporting"
    echo "=============================================="
    echo -e "${NC}"

    echo -e "${CYAN}Configuration:${NC}"
    echo "  • Backend URL: $BACKEND_URL"
    echo "  • Frontend URL: $FRONTEND_URL"
    echo "  • Timeout: ${CURL_TIMEOUT}s"
    echo "  • Verbose: $VERBOSE"
    echo "  • Save Report: $SAVE_REPORT"
    [ "$SAVE_REPORT" = "true" ] && echo "  • Report File: $REPORT_FILE"
    echo ""
}

# Pre-flight checks
preflight_checks() {
    log "Running pre-flight checks..."

    # Check if curl is available
    if ! command -v curl &> /dev/null; then
        critical_fail "curl is not installed or not in PATH"
        exit 1
    fi

    # Check backend availability
    if ! check_service_availability "$BACKEND_URL/api/employees" "Backend"; then
        critical_fail "Backend service is not reachable at $BACKEND_URL"
        info "Please ensure the backend is running before running security tests"
        exit 1
    fi

    success "Pre-flight checks completed"
}

# ============================================================================
# MAIN SCRIPT EXECUTION
# ============================================================================

# Initialize
show_banner
initialize_report
preflight_checks

echo -e "${PURPLE}Starting comprehensive security verification...${NC}"
echo ""

# ============================================================================
# SECURITY TEST SUITE
# ============================================================================

# Test 1: Backend Service Health Check
test_start "Backend Service Health Check"
if check_http_status "$BACKEND_URL/api/employees" "401" "" "Backend health check" >/dev/null 2>&1; then
    success "Backend is responding and properly secured (401 for protected endpoint)"
else
    actual_status=$(check_http_status "$BACKEND_URL/api/employees" "401" "" "Backend health check" 2>&1)
    critical_fail "Backend health check failed (got $actual_status, expected 401)"
fi

# Test 2: Critical Anonymous Access Protection
test_start "Critical Anonymous Access Protection"
anonymous_failures=0
for endpoint in "${CRITICAL_ENDPOINTS[@]}"; do
    if ! check_http_status "$BACKEND_URL$endpoint" "401" "" "Anonymous access to $endpoint" >/dev/null 2>&1; then
        anonymous_failures=$((anonymous_failures + 1))
        actual_status=$(check_http_status "$BACKEND_URL$endpoint" "401" "" "Anonymous access to $endpoint" 2>&1)
        critical_fail "Anonymous access not blocked for $endpoint (got $actual_status, expected 401)"
    fi
done

if [ $anonymous_failures -eq 0 ]; then
    success "All critical endpoints properly block anonymous access"
else
    critical_fail "$anonymous_failures critical endpoints allow anonymous access"
fi

# Test 3: Admin Endpoints Security
test_start "Admin Endpoints Security"
admin_failures=0
admin_endpoints=("/api/admin/diagnostics" "/api/admin/health")

for endpoint in "${admin_endpoints[@]}"; do
    if ! check_http_status "$BACKEND_URL$endpoint" "401" "" "Admin endpoint $endpoint" >/dev/null 2>&1; then
        admin_failures=$((admin_failures + 1))
        actual_status=$(check_http_status "$BACKEND_URL$endpoint" "401" "" "Admin endpoint $endpoint" 2>&1)
        critical_fail "Admin endpoint $endpoint not properly protected (got $actual_status, expected 401)"
    fi
done

if [ $admin_failures -eq 0 ]; then
    success "All admin endpoints properly protected"
else
    critical_fail "$admin_failures admin endpoints not properly protected"
fi

# Test 4: Invalid Token Rejection
test_start "Invalid Token Rejection"
invalid_token_failures=0
test_tokens=("Bearer invalid-token" "Bearer null" "Bearer undefined")

for token in "${test_tokens[@]}"; do
    if ! check_http_status "$BACKEND_URL/api/employees" "401" "Authorization: $token" "Invalid token test" >/dev/null 2>&1; then
        invalid_token_failures=$((invalid_token_failures + 1))
        actual_status=$(check_http_status "$BACKEND_URL/api/employees" "401" "Authorization: $token" "Invalid token test" 2>&1)
        fail "Invalid token '$token' not properly rejected (got $actual_status, expected 401)"
    fi
done

if [ $invalid_token_failures -eq 0 ]; then
    success "All invalid tokens properly rejected"
else
    fail "$invalid_token_failures invalid tokens not properly rejected"
fi

# Test 5: Comprehensive API Endpoint Security
test_start "Comprehensive API Endpoint Security"
endpoint_failures=0
total_endpoints=${#API_ENDPOINTS[@]}

info "Testing $total_endpoints API endpoints for proper authentication..."
for endpoint in "${API_ENDPOINTS[@]}"; do
    if ! check_http_status "$BACKEND_URL$endpoint" "401" "" "API endpoint $endpoint" >/dev/null 2>&1; then
        endpoint_failures=$((endpoint_failures + 1))
        actual_status=$(check_http_status "$BACKEND_URL$endpoint" "401" "" "API endpoint $endpoint" 2>&1)
        fail "API endpoint $endpoint not properly protected (got $actual_status, expected 401)"
    fi
done

if [ $endpoint_failures -eq 0 ]; then
    success "All $total_endpoints API endpoints properly protected"
else
    fail "$endpoint_failures out of $total_endpoints API endpoints not properly protected"
fi

# Test 6: Malformed JWT Token Rejection
test_start "Malformed JWT Token Rejection"
malformed_failures=0
total_malformed=${#MALFORMED_TOKENS[@]}

info "Testing $total_malformed malformed token scenarios..."
for token in "${MALFORMED_TOKENS[@]}"; do
    if ! check_http_status "$BACKEND_URL/api/employees" "401" "Authorization: $token" "Malformed token test" >/dev/null 2>&1; then
        malformed_failures=$((malformed_failures + 1))
        actual_status=$(check_http_status "$BACKEND_URL/api/employees" "401" "Authorization: $token" "Malformed token test" 2>&1)
        fail "Malformed token '$token' not properly rejected (got $actual_status, expected 401)"
    fi
done

if [ $malformed_failures -eq 0 ]; then
    success "All $total_malformed malformed tokens properly rejected"
else
    fail "$malformed_failures out of $total_malformed malformed tokens not properly rejected"
fi

# Test 7: Security Headers Verification
test_start "Security Headers Verification"
headers=$(curl -s -I --max-time $CURL_TIMEOUT "$BACKEND_URL/api/employees" 2>/dev/null || echo "")
security_issues=0

# Check for X-Powered-By header (should be hidden)
if echo "$headers" | grep -qi "x-powered-by"; then
    fail "X-Powered-By header exposed (security risk)"
    security_issues=$((security_issues + 1))
else
    success "X-Powered-By header properly hidden"
fi

# Check for basic security headers (informational)
if echo "$headers" | grep -qi "x-frame-options\|x-content-type-options\|x-xss-protection"; then
    info "Some security headers detected"
else
    warning "No security headers detected (consider implementing Helmet.js in Phase 1)"
fi

# Test 8: Database Connection Verification
test_start "Database Connection Verification"
db_test_endpoints=("/api/employees" "/api/vehicles" "/api/delegations")
db_failures=0

for endpoint in "${db_test_endpoints[@]}"; do
    if ! check_http_status "$BACKEND_URL$endpoint" "401" "" "Database connection test via $endpoint" >/dev/null 2>&1; then
        db_failures=$((db_failures + 1))
        actual_status=$(check_http_status "$BACKEND_URL$endpoint" "401" "" "Database connection test via $endpoint" 2>&1)
        fail "Database connection issue via $endpoint (got $actual_status, expected 401)"
    fi
done

if [ $db_failures -eq 0 ]; then
    success "Database connection working (all endpoints responding with proper auth checks)"
else
    fail "$db_failures database connection issues detected"
fi

# Test 9: Performance and Response Time Check
test_start "Performance and Response Time Check"
slow_responses=0
response_threshold=2000  # 2 seconds

info "Testing response times (threshold: ${response_threshold}ms)..."
for endpoint in "${CRITICAL_ENDPOINTS[@]}"; do
    start_time=$(date +%s%N)
    check_http_status "$BACKEND_URL$endpoint" "401" "" "Performance test" >/dev/null 2>&1
    end_time=$(date +%s%N)
    response_time=$(( (end_time - start_time) / 1000000 ))

    if [ $response_time -gt $response_threshold ]; then
        slow_responses=$((slow_responses + 1))
        warning "Slow response from $endpoint: ${response_time}ms (threshold: ${response_threshold}ms)"
    else
        [ "$VERBOSE" = "true" ] && info "Good response time for $endpoint: ${response_time}ms"
    fi
done

if [ $slow_responses -eq 0 ]; then
    success "All endpoints responding within acceptable time limits"
else
    warning "$slow_responses endpoints have slow response times"
fi

# Test 10: Edge Case Security Tests
test_start "Edge Case Security Tests"
edge_case_failures=0

# Test with empty Authorization header
if ! check_http_status "$BACKEND_URL/api/employees" "401" "Authorization:" "Empty auth header" >/dev/null 2>&1; then
    edge_case_failures=$((edge_case_failures + 1))
    fail "Empty Authorization header not properly handled"
fi

# Test with malformed Authorization header
if ! check_http_status "$BACKEND_URL/api/employees" "401" "Authorization: NotBearer token" "Malformed auth header" >/dev/null 2>&1; then
    edge_case_failures=$((edge_case_failures + 1))
    fail "Malformed Authorization header not properly handled"
fi

# Test with very long token
long_token="Bearer $(printf 'a%.0s' {1..1000})"
if ! check_http_status "$BACKEND_URL/api/employees" "401" "$long_token" "Long token test" >/dev/null 2>&1; then
    edge_case_failures=$((edge_case_failures + 1))
    fail "Very long token not properly handled"
fi

if [ $edge_case_failures -eq 0 ]; then
    success "All edge case security tests passed"
else
    fail "$edge_case_failures edge case security tests failed"
fi

# ============================================================================
# FINAL RESULTS AND REPORTING
# ============================================================================

echo ""
echo -e "${PURPLE}=============================================="
echo "  🔐 SECURITY VERIFICATION RESULTS"
echo "===============================================${NC}"

# Calculate comprehensive results
total_issues=$((FAILED_TESTS + CRITICAL_FAILURES))
if [ $TOTAL_TESTS -gt 0 ]; then
    percentage=$((PASSED_TESTS * 100 / TOTAL_TESTS))
else
    percentage=0
fi

# Display detailed summary
echo ""
echo -e "${CYAN}📊 Comprehensive Test Summary:${NC}"
echo "  • Total Tests Executed: $TOTAL_TESTS"
echo "  • Tests Passed: $PASSED_TESTS"
echo "  • Tests Failed: $FAILED_TESTS"
echo "  • Critical Failures: $CRITICAL_FAILURES"
echo "  • Warnings: $WARNING_TESTS"
echo "  • Overall Success Rate: $percentage%"
echo "  • Report File: $REPORT_FILE"

# Security assessment
echo ""
echo -e "${CYAN}🛡️ Security Assessment:${NC}"

if [ $CRITICAL_FAILURES -gt 0 ]; then
    echo -e "${RED}🚨 CRITICAL SECURITY ISSUES DETECTED"
    echo "❌ $CRITICAL_FAILURES critical security failures found"
    echo "🔧 IMMEDIATE ACTION REQUIRED before deployment"
    echo "📋 Review and fix all critical issues${NC}"
    security_level="CRITICAL"
elif [ $FAILED_TESTS -gt 0 ]; then
    echo -e "${YELLOW}⚠️  SECURITY ISSUES DETECTED"
    echo "❌ $FAILED_TESTS security tests failed"
    echo "🔧 Address failed tests before proceeding"
    echo "📋 Review and fix security issues${NC}"
    security_level="MODERATE"
elif [ $WARNING_TESTS -gt 0 ]; then
    echo -e "${YELLOW}⚠️  MOSTLY SECURE - Minor Issues"
    echo "✅ Core security features working"
    echo "⚠️  $WARNING_TESTS warnings found"
    echo "📋 Consider addressing warnings for optimal security${NC}"
    security_level="GOOD"
else
    echo -e "${GREEN}🎉 ALL SECURITY TESTS PASSED!"
    echo "✅ Staging environment is secure and ready"
    echo "✅ Hybrid RBAC system is functioning correctly"
    echo "✅ Phase 0 security verification complete${NC}"
    security_level="EXCELLENT"
fi

# Next steps based on results
echo ""
echo -e "${CYAN}🚀 Recommended Next Steps:${NC}"
case $security_level in
    "EXCELLENT")
        echo "  1. ✅ Phase 0 Complete - Security foundation solid"
        echo "  2. 🔄 Begin Phase 1: Security Hardening"
        echo "  3. 📋 Implement Docker security fixes"
        echo "  4. 🛡️ Add security headers and rate limiting"
        echo "  5. 🚀 Prepare for production deployment"
        ;;
    "GOOD")
        echo "  1. 📋 Review and address warnings"
        echo "  2. ✅ Core security is functional"
        echo "  3. 🔄 Consider Phase 1 security hardening"
        echo "  4. 🧪 Run additional manual tests"
        ;;
    "MODERATE"|"CRITICAL")
        echo "  1. � Fix all failed security tests immediately"
        echo "  2. 🧪 Re-run security verification"
        echo "  3. 📋 Review authentication and authorization"
        echo "  4. ⚠️  DO NOT deploy until issues are resolved"
        ;;
esac

# Technical details
echo ""
echo -e "${CYAN}📋 Technical Security Details:${NC}"
echo "  • Anonymous access protection: Critical for data security"
echo "  • Admin endpoint protection: Prevents unauthorized admin access"
echo "  • Token validation: Ensures only valid authentication"
echo "  • API endpoint security: Comprehensive protection across all routes"
echo "  • Performance monitoring: Response time validation"
echo "  • Edge case handling: Malformed request protection"

# Manual verification recommendations
echo ""
echo -e "${CYAN}🔍 Manual Verification Recommended:${NC}"
echo "  1. Test login flow in browser: $FRONTEND_URL"
echo "  2. Verify JWT custom claims in browser console"
echo "  3. Test role-based access with different user types"
echo "  4. Confirm RLS policies in database dashboard"
echo "  5. Review application logs for security events"

# Save final report
if [ "$SAVE_REPORT" = "true" ]; then
    cat >> "$REPORT_FILE" << EOF

=== FINAL SECURITY ASSESSMENT ===
Security Level: $security_level
Total Tests: $TOTAL_TESTS
Passed: $PASSED_TESTS
Failed: $FAILED_TESTS
Critical Failures: $CRITICAL_FAILURES
Warnings: $WARNING_TESTS
Success Rate: $percentage%
Timestamp: $(date)

EOF
    echo ""
    echo -e "${GREEN}📄 Detailed report saved to: $REPORT_FILE${NC}"
fi

# Final status
echo ""
if [ $CRITICAL_FAILURES -eq 0 ] && [ $FAILED_TESTS -eq 0 ]; then
    success "🎉 Security verification completed successfully!"
    exit 0
elif [ $CRITICAL_FAILURES -gt 0 ]; then
    critical_fail "🚨 Security verification completed with CRITICAL issues!"
    exit 2
else
    fail "⚠️ Security verification completed with issues!"
    exit 1
fi
